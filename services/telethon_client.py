"""
Telethon client for advanced Telegram operations.
Handles channel message iteration, historical indexing, and advanced media operations.
"""

import os
import asyncio
import logging
from typing import Dict, List, Optional, AsyncGenerator
from datetime import datetime, timedelta
from telethon import TelegramClient, events
from telethon.tl.types import (
    MessageMediaDocument, MessageMediaPhoto, MessageMediaVideo,
    DocumentAttributeFilename, DocumentAttributeVideo, DocumentAttributeAudio
)
from telethon.errors import (
    SessionPasswordNeededError, FloodWaitError, 
    ChannelPrivateError, ChatAdminRequiredError
)

from config.mongodb import get_collection
from utils.metadata_extractor import extract_movie_metadata
from config.search_manager import index_movie_file

logger = logging.getLogger(__name__)


class TelethonIndexingClient:
    """Telethon client for advanced channel indexing operations."""
    
    def __init__(self):
        self.api_id = int(os.getenv('TELEGRAM_API_ID'))
        self.api_hash = os.getenv('TELEGRAM_API_HASH')
        self.bot_token = os.getenv('BOT_TOKEN')
        
        if not all([self.api_id, self.api_hash, self.bot_token]):
            raise ValueError("Missing required Telegram API credentials")
        
        # Initialize Telethon client
        self.client = TelegramClient(
            'cognito_indexing_session',
            self.api_id,
            self.api_hash
        )
        
        self.media_collection = get_collection('media_files')
        self.is_connected = False
        
    async def start(self) -> bool:
        """Start the Telethon client."""
        try:
            logger.info("Starting Telethon client...")
            await self.client.start(bot_token=self.bot_token)
            self.is_connected = True
            logger.info("✅ Telethon client started successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to start Telethon client: {e}")
            return False
    
    async def stop(self):
        """Stop the Telethon client."""
        if self.is_connected:
            await self.client.disconnect()
            self.is_connected = False
            logger.info("Telethon client disconnected")
    
    async def get_channel_info(self, channel_id: int) -> Optional[Dict]:
        """Get detailed channel information."""
        try:
            entity = await self.client.get_entity(channel_id)
            return {
                'id': entity.id,
                'title': entity.title,
                'username': entity.username,
                'participants_count': getattr(entity, 'participants_count', None),
                'about': getattr(entity, 'about', None),
                'access_hash': entity.access_hash
            }
        except Exception as e:
            logger.error(f"Error getting channel info for {channel_id}: {e}")
            return None
    
    async def iter_channel_messages(
        self, 
        channel_id: int, 
        limit: Optional[int] = None,
        offset_date: Optional[datetime] = None,
        reverse: bool = False
    ) -> AsyncGenerator[Dict, None]:
        """
        Iterate through channel messages with media.
        
        Args:
            channel_id: Channel ID to scan
            limit: Maximum number of messages to process
            offset_date: Start from this date (None = from beginning)
            reverse: If True, iterate from oldest to newest
        """
        try:
            entity = await self.client.get_entity(channel_id)
            logger.info(f"Starting message iteration for channel: {entity.title}")
            
            message_count = 0
            async for message in self.client.iter_messages(
                entity,
                limit=limit,
                offset_date=offset_date,
                reverse=reverse
            ):
                message_count += 1
                if message_count % 100 == 0:
                    logger.info(f"Processed {message_count} messages...")
                
                # Only process messages with media
                if not message.media:
                    continue
                
                # Extract media information
                media_info = await self._extract_media_info(message)
                if media_info:
                    yield media_info
                    
        except ChannelPrivateError:
            logger.error(f"Channel {channel_id} is private or bot lacks access")
        except ChatAdminRequiredError:
            logger.error(f"Admin rights required for channel {channel_id}")
        except FloodWaitError as e:
            logger.warning(f"Rate limited, waiting {e.seconds} seconds...")
            await asyncio.sleep(e.seconds)
        except Exception as e:
            logger.error(f"Error iterating messages for channel {channel_id}: {e}")
    
    async def _extract_media_info(self, message) -> Optional[Dict]:
        """Extract media information from a Telethon message."""
        try:
            if not message.media:
                return None
            
            media_info = {
                'message_id': message.id,
                'date': message.date,
                'channel_id': message.peer_id.channel_id if hasattr(message.peer_id, 'channel_id') else None,
                'caption': message.text or '',
                'media_type': None,
                'file_name': None,
                'file_size': None,
                'mime_type': None,
                'duration': None,
                'width': None,
                'height': None
            }
            
            # Handle different media types
            if isinstance(message.media, MessageMediaDocument):
                document = message.media.document
                media_info.update({
                    'media_type': 'document',
                    'file_size': document.size,
                    'mime_type': document.mime_type,
                    'file_id': str(document.id),
                    'file_unique_id': f"{document.id}_{document.access_hash}"
                })
                
                # Extract attributes
                for attr in document.attributes:
                    if isinstance(attr, DocumentAttributeFilename):
                        media_info['file_name'] = attr.file_name
                    elif isinstance(attr, DocumentAttributeVideo):
                        media_info.update({
                            'duration': attr.duration,
                            'width': attr.w,
                            'height': attr.h
                        })
                        if not media_info['file_name']:
                            media_info['media_type'] = 'video'
                    elif isinstance(attr, DocumentAttributeAudio):
                        media_info.update({
                            'duration': attr.duration,
                            'media_type': 'audio'
                        })
                        if hasattr(attr, 'title') and attr.title:
                            media_info['file_name'] = attr.title
            
            elif isinstance(message.media, MessageMediaPhoto):
                photo = message.media.photo
                media_info.update({
                    'media_type': 'photo',
                    'file_id': str(photo.id),
                    'file_unique_id': f"{photo.id}_{photo.access_hash}",
                    'file_size': None  # Photos don't have a direct size in the API
                })
            
            # Set default filename if none found
            if not media_info['file_name']:
                media_info['file_name'] = f"file_{media_info['file_id']}.{self._get_extension(media_info['mime_type'])}"
            
            return media_info
            
        except Exception as e:
            logger.error(f"Error extracting media info: {e}")
            return None
    
    def _get_extension(self, mime_type: str) -> str:
        """Get file extension from MIME type."""
        mime_map = {
            'video/mp4': 'mp4',
            'video/avi': 'avi',
            'video/mkv': 'mkv',
            'video/mov': 'mov',
            'audio/mp3': 'mp3',
            'audio/mpeg': 'mp3',
            'audio/wav': 'wav',
            'audio/flac': 'flac',
            'application/pdf': 'pdf',
            'application/zip': 'zip',
            'image/jpeg': 'jpg',
            'image/png': 'png'
        }
        return mime_map.get(mime_type, 'bin')
    
    async def index_channel_messages(
        self, 
        channel_id: int, 
        limit: Optional[int] = None,
        force_reindex: bool = False
    ) -> Dict[str, int]:
        """
        Index all media messages from a channel.
        
        Args:
            channel_id: Channel ID to index
            limit: Maximum messages to process (None = all)
            force_reindex: If True, re-index existing files
            
        Returns:
            Dictionary with indexing statistics
        """
        stats = {
            'files_found': 0,
            'files_indexed': 0,
            'files_skipped': 0,
            'errors': 0
        }
        
        if not self.is_connected:
            logger.error("Telethon client not connected")
            stats['errors'] += 1
            return stats
        
        try:
            logger.info(f"Starting Telethon indexing for channel {channel_id}")
            
            # Get existing file IDs to avoid duplicates
            existing_files = set()
            if not force_reindex:
                cursor = self.media_collection.find(
                    {'channel_id': channel_id}, 
                    {'file_id': 1}
                )
                existing_files = {doc['file_id'] for doc in cursor if 'file_id' in doc}
                logger.info(f"Found {len(existing_files)} existing files in database")
            
            # Iterate through channel messages
            async for media_info in self.iter_channel_messages(channel_id, limit):
                stats['files_found'] += 1
                
                # Skip if already indexed (unless force reindex)
                if not force_reindex and media_info['file_id'] in existing_files:
                    stats['files_skipped'] += 1
                    continue
                
                try:
                    # Index the media file
                    await self._index_media_file(media_info)
                    stats['files_indexed'] += 1
                    
                    if stats['files_indexed'] % 10 == 0:
                        logger.info(f"Indexed {stats['files_indexed']} files so far...")
                        
                except Exception as e:
                    logger.error(f"Error indexing file {media_info.get('file_name', 'unknown')}: {e}")
                    stats['errors'] += 1
            
            logger.info(f"Telethon indexing completed for channel {channel_id}: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error in Telethon channel indexing: {e}")
            stats['errors'] += 1
            return stats
    
    async def _index_media_file(self, media_info: Dict) -> None:
        """Index a single media file."""
        try:
            # Extract movie metadata
            metadata = {}
            if media_info['file_name']:
                try:
                    metadata = await extract_movie_metadata(media_info['file_name'])
                except Exception as e:
                    logger.debug(f"Could not extract metadata for {media_info['file_name']}: {e}")
            
            # Prepare document for database
            file_data = {
                **media_info,
                **metadata,
                'indexed_at': datetime.utcnow(),
                'indexing_method': 'telethon',
                'is_active': True
            }
            
            # Store in database
            await self.media_collection.insert_one(file_data)
            
            # Index for search
            await index_movie_file(
                file_id=media_info['file_id'],
                file_name=media_info['file_name'],
                file_type=media_info['media_type'],
                metadata=file_data
            )
            
            logger.debug(f"Indexed: {media_info['file_name']}")
            
        except Exception as e:
            logger.error(f"Error indexing media file: {e}")
            raise


# Global Telethon client instance
telethon_client: Optional[TelethonIndexingClient] = None


async def get_telethon_client() -> Optional[TelethonIndexingClient]:
    """Get or create the global Telethon client."""
    global telethon_client
    
    if telethon_client is None:
        telethon_client = TelethonIndexingClient()
        if not await telethon_client.start():
            telethon_client = None
    
    return telethon_client


async def cleanup_telethon_client():
    """Cleanup the global Telethon client."""
    global telethon_client
    
    if telethon_client:
        await telethon_client.stop()
        telethon_client = None
