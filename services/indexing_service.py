"""
Indexing service for the Cognito Movie Management Bot.
Handles background monitoring of channels and automatic indexing of movie files.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Set
from datetime import datetime, timedelta
from telegram import Bo<PERSON>

from config.mongodb import get_collection
from config.channel_manager import get_active_channels
from utils.metadata_extractor import extract_movie_metadata
from config.search_manager import index_movie_file

logger = logging.getLogger(__name__)


class IndexingService:
    """Service for indexing movie files from monitored channels."""

    def __init__(self, telegram_bot: Bot):
        self.bot = telegram_bot
        self.media_collection = get_collection('media_files')
        self.channels_collection = get_collection('channels')
        self.is_running = False
        self.indexing_task: Optional[asyncio.Task] = None
        self.indexed_files: Set[str] = set()  # Track indexed file_ids to avoid duplicates

    async def start_background_indexing(self, poll_interval: int = 300) -> None:
        """
        Start background indexing service.

        Args:
            poll_interval: Time in seconds between channel checks (default 5 minutes)
        """
        if self.is_running:
            logger.warning("Indexing service is already running")
            return

        self.is_running = True
        logger.info(f"Starting background indexing service (poll interval: {poll_interval}s)")

        # Load previously indexed files
        await self._load_indexed_files()

        # Start the indexing loop
        self.indexing_task = asyncio.create_task(self._indexing_loop(poll_interval))

    async def stop_background_indexing(self) -> None:
        """Stop the background indexing service."""
        if not self.is_running:
            logger.warning("Indexing service is not running")
            return

        self.is_running = False
        if self.indexing_task:
            self.indexing_task.cancel()
            try:
                await self.indexing_task
            except asyncio.CancelledError:
                pass

        logger.info("Background indexing service stopped")

    async def index_channel(self, channel_id: int, force_reindex: bool = False) -> Dict[str, int]:
        """
        Index all movie files from a specific channel.

        Args:
            channel_id: Telegram channel ID
            force_reindex: If True, re-index all files even if already indexed

        Returns:
            Dictionary with indexing statistics
        """
        stats = {
            'files_found': 0,
            'files_indexed': 0,
            'files_skipped': 0,
            'errors': 0
        }

        try:
            logger.info(f"Starting indexing for channel {channel_id}")

            # If force reindex, clear existing data for this channel
            if force_reindex:
                await self._clear_channel_data(channel_id)
                logger.info(f"Cleared existing data for channel {channel_id}")

            # Get all messages with media from the channel
            # Note: python-telegram-bot doesn't have a direct equivalent to iter_messages
            # We'll need to implement this differently or use a different approach
            logger.warning(f"Channel indexing not fully implemented for channel {channel_id} - python-telegram-bot limitations")

        except Exception as e:
            logger.error(f"Error indexing channel {channel_id}: {e}")
            stats['errors'] += 1

        return stats

    async def index_all_channels(self) -> Dict[str, Dict[str, int]]:
        """
        Index all active monitored channels.

        Returns:
            Dictionary mapping channel IDs to their indexing statistics
        """
        results = {}
        channels = await get_active_channels()

        for channel in channels:
            channel_id = channel['channel_id']
            logger.info(f"Indexing channel {channel_id} ({channel.get('channel_username', 'unknown')})")

            stats = await self.index_channel(channel_id)
            results[str(channel_id)] = stats

            # Delay between channels to be respectful to Telegram API
            await asyncio.sleep(1)

        return results

    async def reindex_channel(self, channel_id: int) -> Dict[str, int]:
        """
        Re-index a channel by clearing existing data and re-indexing from scratch.

        Args:
            channel_id: Telegram channel ID

        Returns:
            Indexing statistics
        """
        logger.info(f"Starting re-indexing for channel {channel_id}")

        # Temporarily disable monitoring
        await self._set_channel_monitoring(channel_id, False)

        try:
            # Clear existing data
            await self._clear_channel_data(channel_id)

            # Re-index
            stats = await self.index_channel(channel_id, force_reindex=True)

            logger.info(f"Re-indexing completed for channel {channel_id}: {stats}")
            return stats

        finally:
            # Re-enable monitoring
            await self._set_channel_monitoring(channel_id, True)

    async def rescan_channel(self, channel_id: int, user_id: int) -> bool:
        """
        Rescan a channel for the channel handler.
        This is a wrapper around reindex_channel for UI purposes.

        Args:
            channel_id: Telegram channel ID
            user_id: User who initiated the rescan

        Returns:
            True if successful, False otherwise
        """
        try:
            stats = await self.reindex_channel(channel_id)
            logger.info(f"Channel rescan completed for {channel_id} by user {user_id}: {stats}")
            return True
        except Exception as e:
            logger.error(f"Error rescanning channel {channel_id}: {e}")
            return False

    async def _indexing_loop(self, poll_interval: int) -> None:
        """Main indexing loop that periodically checks for new files."""
        while self.is_running:
            try:
                logger.info("Starting scheduled indexing check")

                # Index all active channels
                results = await self.index_all_channels()

                # Log summary
                total_indexed = sum(stats.get('files_indexed', 0) for stats in results.values())
                if total_indexed > 0:
                    logger.info(f"Scheduled indexing completed: {total_indexed} new files indexed")
                else:
                    logger.info("Scheduled indexing completed: no new files found")

            except Exception as e:
                logger.error(f"Error in indexing loop: {e}")

            # Wait for next check
            await asyncio.sleep(poll_interval)

    async def _index_movie_file(self, message_data: Dict, channel_id: int) -> None:
        """Index a single movie file from Telegram message data."""
        try:
            # Extract metadata from filename
            filename = message_data.get('filename', 'unknown_file')
            telegram_data = message_data

            metadata = extract_movie_metadata(filename, telegram_data)

            # Add channel and message info
            metadata.update({
                'channel_id': channel_id,
                'message_id': message_data.get('message_id'),
                'indexed_at': datetime.utcnow(),
                'last_updated': datetime.utcnow(),
                'is_active': True
            })

            # Store in database
            result = await self.media_collection.insert_one(metadata)

            # Index for search
            await index_movie_file(
                file_id=metadata['file_id'],
                file_name=filename,
                file_type=metadata.get('file_type', 'movie'),
                metadata=metadata
            )

            logger.debug(f"Indexed movie file: {filename} (ID: {metadata['file_id']})")

        except Exception as e:
            logger.error(f"Error indexing movie file: {e}")
            raise

    async def _load_indexed_files(self) -> None:
        """Load set of already indexed file IDs to avoid duplicates."""
        try:
            # Get all file_ids from the database
            cursor = self.media_collection.find({}, {'file_id': 1})
            self.indexed_files = {doc['file_id'] async for doc in cursor}
            logger.info(f"Loaded {len(self.indexed_files)} previously indexed files")

        except Exception as e:
            logger.error(f"Error loading indexed files: {e}")
            self.indexed_files = set()

    async def _clear_channel_data(self, channel_id: int) -> None:
        """Clear all indexed data for a specific channel."""
        try:
            # Remove from media collection
            result = await self.media_collection.delete_many({'channel_id': channel_id})

            # Remove from search index (this would need to be implemented in search_manager)
            # For now, we'll rely on the search index being rebuilt

            # Remove from our tracking set
            # Note: This is approximate since we don't track per channel
            self.indexed_files.clear()  # Clear and reload
            await self._load_indexed_files()

            logger.info(f"Cleared {result.deleted_count} documents for channel {channel_id}")

        except Exception as e:
            logger.error(f"Error clearing channel data for {channel_id}: {e}")

    async def _set_channel_monitoring(self, channel_id: int, enabled: bool) -> None:
        """Enable or disable monitoring for a channel."""
        try:
            await self.channels_collection.update_one(
                {'channel_id': channel_id},
                {'$set': {'is_monitored': enabled, 'updated_at': datetime.utcnow()}}
            )
        except Exception as e:
            logger.error(f"Error setting monitoring for channel {channel_id}: {e}")


# Global instance (will be initialized with Telegram bot)
indexing_service: Optional[IndexingService] = None


def get_indexing_service() -> Optional[IndexingService]:
    """Get the global indexing service instance."""
    return indexing_service


def set_indexing_service(service: IndexingService) -> None:
    """Set the global indexing service instance."""
    global indexing_service
    indexing_service = service