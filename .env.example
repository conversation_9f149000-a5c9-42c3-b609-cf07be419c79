# =============================================================================
# TELEGRAM BOT CONFIGURATION
# =============================================================================

# Telegram Bot Token (Required)
# Get this from @BotFather on Telegram
BOT_TOKEN=YOUR_TELEGRAM_BOT_TOKEN

# Telegram API Credentials (Required for advanced features)
# Get these from https://my.telegram.org/apps
# Required for: channel monitoring, user client operations, advanced media handling
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash

# Bot Configuration
BOT_USERNAME=your_bot_username
BOT_NAME="Media Management Bot"
BOT_DESCRIPTION="A comprehensive media management bot for Telegram channels"

# Super Admin Configuration (bootstrap admin - can manage other admins through bot)
SUPER_ADMIN_ID=123456789  # Your Telegram user ID

# =============================================================================
# DATABASE CONFIGURATION (MongoDB Only)
# =============================================================================

# MongoDB Connection URI (contains all connection details)
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/database_name
# For local MongoDB: mongodb://localhost:27017/database_name
MONGODB_URI=****************************************************************************

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis Configuration (for caching and session management)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# ELASTICSEARCH CONFIGURATION
# =============================================================================

# Elasticsearch Configuration (for search functionality)
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_INDEX=media_files
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
ELASTICSEARCH_URL=http://localhost:9200

# =============================================================================
# MEDIA PROCESSING CONFIGURATION
# =============================================================================

# Movie File Storage Configuration
MEDIA_STORAGE_PATH=./data/movies
TEMP_STORAGE_PATH=./data/temp
MAX_FILE_SIZE=5368709120  # 5GB in bytes (movies are typically larger)

# Movie File Extensions (prioritized by quality/compatibility)
ALLOWED_EXTENSIONS=mp4,mkv,avi,mov,m4v,wmv,webm,flv,3gp,mpg,mpeg,ts,m2ts,3gp,mp3,flac,wav,aac,ogg,wma,m4a,jpg,jpeg,png,gif,bmp,webp,tiff,pdf,txt,doc,docx,zip,rar,7z

# FFmpeg Configuration
FFMPEG_PATH=/usr/bin/ffmpeg
FFPROBE_PATH=/usr/bin/ffprobe

# Image Processing
MAX_IMAGE_SIZE=10485760  # 10MB in bytes
THUMBNAIL_SIZE=300,300
IMAGE_QUALITY=85

# =============================================================================
# CHANNEL MANAGEMENT (Dynamic - managed through bot commands)
# =============================================================================

# Channel Processing Configuration (global settings)
AUTO_INDEX_NEW_FILES=true
INDEX_INTERVAL_HOURS=24
DUPLICATE_CHECK_ENABLED=true

# =============================================================================
# SEARCH CONFIGURATION
# =============================================================================

# Search Settings
SEARCH_RESULTS_LIMIT=50
FUZZY_SEARCH_THRESHOLD=0.8
SEARCH_CACHE_TTL=3600  # 1 hour in seconds

# Indexing Configuration
AUTO_INDEX_ON_STARTUP=false
INDEX_BATCH_SIZE=100
INDEX_TIMEOUT_SECONDS=300

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Logging Settings
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE_PATH=./logs/bot.log
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5

# Feature-specific logging
LOG_MEDIA_PROCESSING=true
LOG_USER_ACTIONS=true
LOG_ADMIN_ACTIONS=true
LOG_SEARCH_QUERIES=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=30
RATE_LIMIT_WINDOW=60  # seconds

# User Management
MAX_USERS_PER_CHANNEL=10000
USER_TIMEOUT_MINUTES=30
BAN_DURATION_HOURS=24

# Security Settings
ENABLE_WEBHOOK=false
WEBHOOK_URL=https://your-domain.com/webhook
WEBHOOK_SECRET=your_webhook_secret

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================

# Prometheus Metrics
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=8000

# Sentry Error Tracking
SENTRY_DSN=
SENTRY_ENVIRONMENT=development

# Statistics
STATS_UPDATE_INTERVAL=3600  # 1 hour in seconds
KEEP_STATS_DAYS=30

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Support & Links
SUPPORT_LINK=https://t.me/your_support_channel
GROUP_LINK=https://t.me/your_group
HELP_LINK=https://your-help-documentation.com
DONATION_LINK=https://your-donation-link.com

# API Keys (if needed for external services)
TMDB_API_KEY=your_tmdb_api_key
OMDB_API_KEY=your_omdb_api_key

# Unsplash API for welcome message posters
UNSPLASH_ACCESS_KEY=your_unsplash_access_key
UNSPLASH_SECRET_KEY=your_unsplash_secret_key

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Environment
ENVIRONMENT=development  # development, staging, production
DEBUG=true

# Testing
TEST_DATABASE_URL=sqlite:///./data/test.db
TEST_REDIS_URL=redis://localhost:6379/1

# Development Tools
ENABLE_DEBUG_COMMANDS=true
MOCK_EXTERNAL_SERVICES=false
