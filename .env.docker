# =============================================================================
# DOCKER ENVIRONMENT CONFIGURATION
# This file contains environment variables optimized for Docker deployment
# =============================================================================

# =============================================================================
# TELEGRAM BOT CONFIGURATION
# =============================================================================

BOT_TOKEN=YOUR_TELEGRAM_BOT_TOKEN

# Telegram API Credentials (Required for advanced features)
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash

BOT_USERNAME=your_bot_username
BOT_NAME=Media Management Bot
BOT_DESCRIPTION=A comprehensive media management bot for Telegram channels

# Super Admin Configuration (bootstrap admin)
SUPER_ADMIN_ID=123456789

# =============================================================================
# DATABASE CONFIGURATION (MongoDB Only)
# =============================================================================

# MongoDB Connection URI (contains all connection details)
MONGODB_URI=****************************************************************************************

# =============================================================================
# REDIS CONFIGURATION (Docker Service)
# =============================================================================

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://redis:6379/0

# =============================================================================
# ELASTICSEARCH CONFIGURATION (Docker Service)
# =============================================================================

ELASTICSEARCH_HOST=elasticsearch
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_INDEX=media_files
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
ELASTICSEARCH_URL=http://elasticsearch:9200

# =============================================================================
# MEDIA PROCESSING CONFIGURATION
# =============================================================================

MEDIA_STORAGE_PATH=/app/data/media
TEMP_STORAGE_PATH=/app/data/temp
MAX_FILE_SIZE=2147483648
ALLOWED_EXTENSIONS=mp4,mkv,avi,mov,wmv,flv,webm,m4v,3gp,mp3,flac,wav,aac,ogg,wma,m4a,jpg,jpeg,png,gif,bmp,webp,tiff,pdf,txt,doc,docx,zip,rar,7z

# FFmpeg Configuration (Docker paths)
FFMPEG_PATH=/usr/bin/ffmpeg
FFPROBE_PATH=/usr/bin/ffprobe

# Image Processing
MAX_IMAGE_SIZE=10485760
THUMBNAIL_SIZE=300,300
IMAGE_QUALITY=85

# =============================================================================
# CHANNEL MANAGEMENT (Dynamic - managed through bot commands)
# =============================================================================

# Channel Processing Configuration
AUTO_INDEX_NEW_FILES=true
INDEX_INTERVAL_HOURS=24
DUPLICATE_CHECK_ENABLED=true

# =============================================================================
# SEARCH CONFIGURATION
# =============================================================================

SEARCH_RESULTS_LIMIT=50
FUZZY_SEARCH_THRESHOLD=0.8
SEARCH_CACHE_TTL=3600
AUTO_INDEX_ON_STARTUP=false
INDEX_BATCH_SIZE=100
INDEX_TIMEOUT_SECONDS=300

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE_PATH=/app/logs/bot.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

LOG_MEDIA_PROCESSING=true
LOG_USER_ACTIONS=true
LOG_ADMIN_ACTIONS=true
LOG_SEARCH_QUERIES=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=30
RATE_LIMIT_WINDOW=60

MAX_USERS_PER_CHANNEL=10000
USER_TIMEOUT_MINUTES=30
BAN_DURATION_HOURS=24

ENABLE_WEBHOOK=false
WEBHOOK_URL=https://your-domain.com/webhook
WEBHOOK_SECRET=your_webhook_secret

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================

PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=8000

SENTRY_DSN=
SENTRY_ENVIRONMENT=production

STATS_UPDATE_INTERVAL=3600
KEEP_STATS_DAYS=30

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

SUPPORT_LINK=https://t.me/your_support_channel
GROUP_LINK=https://t.me/your_group
HELP_LINK=https://your-help-documentation.com
DONATION_LINK=https://your-donation-link.com

TMDB_API_KEY=your_tmdb_api_key
OMDB_API_KEY=your_omdb_api_key

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

ENVIRONMENT=production
DEBUG=false

ENABLE_DEBUG_COMMANDS=false
MOCK_EXTERNAL_SERVICES=false
