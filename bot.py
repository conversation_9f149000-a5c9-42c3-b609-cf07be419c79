#!/usr/bin/env python3
"""
Cognito - Movie Management Bot
A Telegram bot for managing and searching movie files from private channels.
"""

import logging
import os
from dotenv import load_dotenv
from telegram import Update
from telegram.ext import (
    Application,
    CommandHandler,
    CallbackQueryHandler,
    MessageHandler,
    filters,
    ContextTypes
)

# Import handlers
from handlers.user.welcome_handler import welcome_handler
from handlers.admin.channel_handler import channel_handler
from handlers.admin.indexing_handler import indexing_handler

# Import database
from config.mongodb import initialize_mongodb

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)


class CognitoBot:
    """Main bot class for Cognito Movie Management Bot."""

    def __init__(self):
        self.bot_token = os.getenv('BOT_TOKEN')
        self.application = None

        if not self.bot_token:
            raise ValueError("BOT_TOKEN not found in environment variables")

        # Initialize database connection
        logger.info("Initializing MongoDB connection...")
        if initialize_mongodb():
            logger.info("✅ MongoDB connection established")
        else:
            logger.error("❌ Failed to connect to MongoDB")

    async def setup_handlers(self):
        """Set up all command and callback handlers."""
        logger.info("Setting up bot handlers...")

        # Welcome commands
        self.application.add_handler(CommandHandler("start", welcome_handler.handle_start_command))
        self.application.add_handler(CommandHandler("intro", welcome_handler.handle_intro_command))

        # Channel management commands
        self.application.add_handler(CommandHandler("channel", channel_handler.handle_channel_command))

        # Indexing commands
        self.application.add_handler(CommandHandler("index", indexing_handler.handle_index_command))

        # Indexing commands
        self.application.add_handler(CommandHandler("index", indexing_handler.handle_index_command))

        # Callback query handlers for inline keyboards with pattern filtering
        self.application.add_handler(CallbackQueryHandler(
            welcome_handler.handle_callback_query,
            pattern="^(help_tutorial|search_tips|admin_panel|bot_stats|back_to_welcome)$"
        ))
        self.application.add_handler(CallbackQueryHandler(
            channel_handler.handle_callback_query,
            pattern="^(manage_channels|add_channel_prompt|refresh_channels|channel_help|manage_channel_.*|toggle_monitoring_.*|toggle_search_.*|ask_remove_.*|confirm_remove_.*|cancel_remove)$"
        ))

        # Error handler
        self.application.add_error_handler(self.error_handler)

        logger.info("Bot handlers setup complete")

    async def handle_media_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle media messages for automatic indexing."""
        try:
            message = update.message
            if not message:
                logger.debug("No message in update")
                return

            logger.info(f"🔍 Received message from chat {message.chat.id} ({message.chat.title}) - Type: {message.chat.type}")

            # Only process messages from channels/supergroups
            if message.chat.type not in ['channel', 'supergroup']:
                logger.info(f"Ignoring message from {message.chat.type} chat")
                return

            # Check if message has media
            has_media = bool(message.document or message.video or message.audio)
            logger.info(f"Message has media: {has_media}")

            if has_media:
                logger.info(f"📁 Processing media message from {message.chat.title}")

                # Get the indexing service
                from services.indexing_service import get_indexing_service
                indexing_service = get_indexing_service()

                if indexing_service:
                    result = await indexing_service.process_channel_message(message)
                    logger.info(f"Media processing result: {result}")
                else:
                    logger.warning("Indexing service not available for processing media message")
            else:
                logger.debug("Message has no media, skipping")

        except Exception as e:
            logger.error(f"Error handling media message: {e}")

    async def handle_debug_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Debug handler to catch all messages."""
        try:
            message = update.message
            if message and message.chat:
                logger.info(f"🐛 DEBUG: Message from {message.chat.id} ({message.chat.title or 'Unknown'}) - Type: {message.chat.type}")
                if message.document:
                    logger.info(f"🐛 DEBUG: Document - {message.document.file_name}")
                elif message.video:
                    logger.info(f"🐛 DEBUG: Video - {getattr(message.video, 'file_name', 'unnamed')}")
                elif message.audio:
                    logger.info(f"🐛 DEBUG: Audio - {getattr(message.audio, 'file_name', 'unnamed')}")
                elif message.text:
                    logger.info(f"🐛 DEBUG: Text message: {message.text[:50]}...")
        except Exception as e:
            logger.error(f"Error in debug message handler: {e}")

    async def error_handler(self, update: Update, context) -> None:
        """Handle errors that occur during bot operation."""
        logger.error(f"Exception while handling an update: {context.error}")

        # Try to send error message to user if possible
        if update and update.effective_chat:
            try:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text="🚫 Sorry, something went wrong. Please try again later."
                )
            except Exception as e:
                logger.error(f"Failed to send error message to user: {e}")

    async def post_init(self, application):
        """Post initialization hook."""
        logger.info("Bot initialization complete")
        logger.info(f"Bot username: @{application.bot.username}")

        # Start background indexing service
        try:
            from services.indexing_service import IndexingService, set_indexing_service
            indexing_service = IndexingService(application.bot)
            set_indexing_service(indexing_service)  # Set the global instance
            await indexing_service.start_background_indexing()
            logger.info("✅ Background indexing service started")
        except Exception as e:
            logger.error(f"❌ Failed to start background indexing service: {e}")

        # Initialize Telethon client for advanced indexing
        try:
            from services.telethon_client import get_telethon_client
            telethon_client = await get_telethon_client()
            if telethon_client:
                logger.info("✅ Telethon client initialized for advanced indexing")
            else:
                logger.warning("⚠️ Telethon client failed to initialize")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Telethon client: {e}")

    async def post_shutdown(self, application):
        """Post shutdown hook."""
        logger.info("Bot shutting down...")

        # Cleanup Telethon client
        try:
            from services.telethon_client import cleanup_telethon_client
            await cleanup_telethon_client()
            logger.info("✅ Telethon client cleaned up")
        except Exception as e:
            logger.error(f"❌ Error cleaning up Telethon client: {e}")

    def start_bot(self):
        """Start the bot."""
        logger.info("Starting Cognito Movie Management Bot...")

        # Create application
        self.application = Application.builder().token(self.bot_token).build()

        # Set up handlers (sync version)
        self.setup_handlers_sync()

        # Set post init and shutdown hooks
        self.application.post_init = self.post_init
        self.application.post_shutdown = self.post_shutdown

        # Start the bot
        logger.info("Bot is starting...")
        self.application.run_polling(
            allowed_updates=Update.ALL_TYPES,
            drop_pending_updates=True
        )

    def setup_handlers_sync(self):
        """Set up all command and callback handlers (sync version)."""
        logger.info("Setting up bot handlers...")

        # Welcome commands
        self.application.add_handler(CommandHandler("start", welcome_handler.handle_start_command))
        self.application.add_handler(CommandHandler("intro", welcome_handler.handle_intro_command))

        # Channel management commands
        self.application.add_handler(CommandHandler("channel", channel_handler.handle_channel_command))

        # Indexing commands
        self.application.add_handler(CommandHandler("index", indexing_handler.handle_index_command))

        # Media message handler for automatic indexing
        self.application.add_handler(MessageHandler(
            filters.Document.ALL | filters.VIDEO | filters.AUDIO,
            self.handle_media_message
        ))

        # Debug: General message handler to catch all messages
        self.application.add_handler(MessageHandler(
            filters.ALL,
            self.handle_debug_message
        ))

        # Callback query handlers for inline keyboards with pattern filtering
        self.application.add_handler(CallbackQueryHandler(
            welcome_handler.handle_callback_query,
            pattern="^(help_tutorial|search_tips|admin_panel|bot_stats|back_to_welcome)$"
        ))
        self.application.add_handler(CallbackQueryHandler(
            channel_handler.handle_callback_query,
            pattern="^(manage_channels|add_channel_prompt|refresh_channels|channel_help|manage_channel_.*|toggle_monitoring_.*|toggle_search_.*|ask_remove_.*|confirm_remove_.*|cancel_remove|rescan_channel_.*|confirm_rescan_.*)$"
        ))
        self.application.add_handler(CallbackQueryHandler(
            indexing_handler.handle_reindex_callback,
            pattern="^(confirm_reindex_.*|cancel_reindex)$"
        ))

        # Error handler
        self.application.add_error_handler(self.error_handler)

        logger.info("Bot handlers setup complete")


def main():
    """Main function to start the bot."""
    try:
        bot = CognitoBot()
        bot.start_bot()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        raise


if __name__ == '__main__':
    main()
