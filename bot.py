#!/usr/bin/env python3
"""
Cognito - Movie Management Bot
A Telegram bot for managing and searching movie files from private channels.
"""

import logging
import os
from dotenv import load_dotenv
from telegram import Update
from telegram.ext import (
    Application,
    Command<PERSON><PERSON>ler,
    Callback<PERSON>uery<PERSON><PERSON><PERSON>,
    MessageHandler,
    filters
)

# Import handlers
from handlers.user.welcome_handler import welcome_handler
from handlers.admin.channel_handler import channel_handler
from handlers.admin.indexing_handler import indexing_handler

# Import database
from config.mongodb import initialize_mongodb

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

logger = logging.getLogger(__name__)


class CognitoBot:
    """Main bot class for Cognito Movie Management Bot."""

    def __init__(self):
        self.bot_token = os.getenv('BOT_TOKEN')
        self.application = None

        if not self.bot_token:
            raise ValueError("BOT_TOKEN not found in environment variables")

        # Initialize database connection
        logger.info("Initializing MongoDB connection...")
        if initialize_mongodb():
            logger.info("✅ MongoDB connection established")
        else:
            logger.error("❌ Failed to connect to MongoDB")

    async def setup_handlers(self):
        """Set up all command and callback handlers."""
        logger.info("Setting up bot handlers...")

        # Welcome commands
        self.application.add_handler(CommandHandler("start", welcome_handler.handle_start_command))
        self.application.add_handler(CommandHandler("intro", welcome_handler.handle_intro_command))

        # Channel management commands
        self.application.add_handler(CommandHandler("channel", channel_handler.handle_channel_command))

        # Indexing commands
        self.application.add_handler(CommandHandler("index", indexing_handler.handle_index_command))

        # Indexing commands
        self.application.add_handler(CommandHandler("index", indexing_handler.handle_index_command))

        # Callback query handlers for inline keyboards with pattern filtering
        self.application.add_handler(CallbackQueryHandler(
            welcome_handler.handle_callback_query,
            pattern="^(help_tutorial|search_tips|admin_panel|bot_stats|back_to_welcome)$"
        ))
        self.application.add_handler(CallbackQueryHandler(
            channel_handler.handle_callback_query,
            pattern="^(manage_channels|add_channel_prompt|refresh_channels|channel_help|manage_channel_.*|toggle_monitoring_.*|toggle_search_.*|ask_remove_.*|confirm_remove_.*|cancel_remove)$"
        ))

        # Error handler
        self.application.add_error_handler(self.error_handler)

        logger.info("Bot handlers setup complete")

    async def error_handler(self, update: Update, context) -> None:
        """Handle errors that occur during bot operation."""
        logger.error(f"Exception while handling an update: {context.error}")

        # Try to send error message to user if possible
        if update and update.effective_chat:
            try:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text="🚫 Sorry, something went wrong. Please try again later."
                )
            except Exception as e:
                logger.error(f"Failed to send error message to user: {e}")

    async def post_init(self, application):
        """Post initialization hook."""
        logger.info("Bot initialization complete")
        logger.info(f"Bot username: @{application.bot.username}")

        # Start background indexing service
        try:
            from services.indexing_service import IndexingService, set_indexing_service
            indexing_service = IndexingService(application.bot)
            set_indexing_service(indexing_service)  # Set the global instance
            await indexing_service.start_background_indexing()
            logger.info("✅ Background indexing service started")
        except Exception as e:
            logger.error(f"❌ Failed to start background indexing service: {e}")

    def start_bot(self):
        """Start the bot."""
        logger.info("Starting Cognito Movie Management Bot...")

        # Create application
        self.application = Application.builder().token(self.bot_token).build()

        # Set up handlers (sync version)
        self.setup_handlers_sync()

        # Set post init hook
        self.application.post_init = self.post_init

        # Start the bot
        logger.info("Bot is starting...")
        self.application.run_polling(
            allowed_updates=Update.ALL_TYPES,
            drop_pending_updates=True
        )

    def setup_handlers_sync(self):
        """Set up all command and callback handlers (sync version)."""
        logger.info("Setting up bot handlers...")

        # Welcome commands
        self.application.add_handler(CommandHandler("start", welcome_handler.handle_start_command))
        self.application.add_handler(CommandHandler("intro", welcome_handler.handle_intro_command))

        # Channel management commands
        self.application.add_handler(CommandHandler("channel", channel_handler.handle_channel_command))

        # Indexing commands
        self.application.add_handler(CommandHandler("index", indexing_handler.handle_index_command))

        # Indexing commands
        self.application.add_handler(CommandHandler("index", indexing_handler.handle_index_command))

        # Callback query handlers for inline keyboards with pattern filtering
        self.application.add_handler(CallbackQueryHandler(
            welcome_handler.handle_callback_query,
            pattern="^(help_tutorial|search_tips|admin_panel|bot_stats|back_to_welcome)$"
        ))
        self.application.add_handler(CallbackQueryHandler(
            channel_handler.handle_callback_query,
            pattern="^(manage_channels|add_channel_prompt|refresh_channels|channel_help|manage_channel_.*|toggle_monitoring_.*|toggle_search_.*|ask_remove_.*|confirm_remove_.*|cancel_remove)$"
        ))

        # Error handler
        self.application.add_error_handler(self.error_handler)

        logger.info("Bot handlers setup complete")


def main():
    """Main function to start the bot."""
    try:
        bot = CognitoBot()
        bot.start_bot()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        raise


if __name__ == '__main__':
    main()