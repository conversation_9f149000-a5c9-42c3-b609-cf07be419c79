# =============================================================================
# RENDER.COM FREE TIER CONFIGURATION
# =============================================================================

# =============================================================================
# TELEGRAM BOT CONFIGURATION
# =============================================================================
BOT_TOKEN=your_telegram_bot_token

# Telegram API Credentials (Required for advanced features)
# Get these from https://my.telegram.org/apps
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash

BOT_USERNAME="Cognito"
BOT_NAME="Media Management Bot"
BOT_DESCRIPTION="A comprehensive media management bot for Telegram channels"

# Super Admin Configuration (bootstrap admin)
SUPER_ADMIN_ID=your_telegram_user_id

# =============================================================================
# DATABASE CONFIGURATION (MongoDB Atlas - Free Tier)
# =============================================================================
# Use MongoDB Atlas free tier (512MB storage)
MONGODB_URI=**********************************************************************

# =============================================================================
# REDIS CONFIGURATION (DISABLED for Render.com free tier)
# =============================================================================
# Redis is NOT available on Render.com free tier
# Bot will use in-memory caching instead (slower but functional)
REDIS_ENABLED=false

# =============================================================================
# SEARCH CONFIGURATION (Optimized for free hosting)
# =============================================================================
# Using Whoosh for better search features while staying free
SEARCH_ENGINE=whoosh
WHOOSH_ENABLED=true
WHOOSH_INDEX_PATH=/tmp/search_index  # Use temp directory on Render

# Elasticsearch disabled for free tier
ELASTICSEARCH_ENABLED=false

# =============================================================================
# MOVIE PROCESSING CONFIGURATION (Optimized for free tier)
# =============================================================================
MEDIA_STORAGE_PATH=/tmp/movies  # Use temp storage on Render
TEMP_STORAGE_PATH=/tmp/temp
MAX_FILE_SIZE=1073741824  # 1GB (reasonable for movie trailers/samples on free tier)
ALLOWED_EXTENSIONS=mp4,mkv,avi,mov,m4v,wmv,webm,flv,3gp,mpg,mpeg

# Movie-specific settings for free tier
GENERATE_THUMBNAILS=true
THUMBNAIL_SIZE=300,450  # Movie poster aspect ratio, smaller for free tier
GENERATE_PREVIEWS=false  # Disabled to save resources on free tier
EXTRACT_MOVIE_INFO=true

# FFmpeg Configuration (if available on Render)
FFMPEG_PATH=/usr/bin/ffmpeg
FFPROBE_PATH=/usr/bin/ffprobe

# Image Processing (reduced for free tier)
MAX_IMAGE_SIZE=5242880  # 5MB
THUMBNAIL_SIZE=200,200  # Smaller thumbnails
IMAGE_QUALITY=75

# =============================================================================
# CHANNEL MANAGEMENT (Dynamic - managed through bot commands)
# =============================================================================
AUTO_INDEX_NEW_FILES=true
INDEX_INTERVAL_HOURS=24
DUPLICATE_CHECK_ENABLED=true

# =============================================================================
# SEARCH CONFIGURATION (Optimized for free tier)
# =============================================================================
SEARCH_RESULTS_LIMIT=25  # Reduced for performance
FUZZY_SEARCH_THRESHOLD=0.8
SEARCH_CACHE_TTL=1800  # 30 minutes (shorter cache)
AUTO_INDEX_ON_STARTUP=false
INDEX_BATCH_SIZE=50  # Smaller batches
INDEX_TIMEOUT_SECONDS=120

# =============================================================================
# LOGGING CONFIGURATION (Optimized for free tier)
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE_PATH=/tmp/bot.log  # Use temp directory
LOG_MAX_SIZE=5242880  # 5MB
LOG_BACKUP_COUNT=2  # Fewer backups

LOG_MEDIA_PROCESSING=true
LOG_USER_ACTIONS=false  # Reduced logging for performance
LOG_ADMIN_ACTIONS=true
LOG_SEARCH_QUERIES=false

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=20  # More restrictive for free tier
RATE_LIMIT_WINDOW=60

MAX_USERS_PER_CHANNEL=1000  # Reduced for free tier
USER_TIMEOUT_MINUTES=15
BAN_DURATION_HOURS=24

ENABLE_WEBHOOK=true  # Recommended for Render.com
WEBHOOK_URL=https://your-app-name.onrender.com/webhook
WEBHOOK_SECRET=your_webhook_secret

# =============================================================================
# MONITORING & ANALYTICS (Disabled for free tier)
# =============================================================================
PROMETHEUS_ENABLED=false  # Disabled for free tier
SENTRY_DSN=  # Optional: Free Sentry account

STATS_UPDATE_INTERVAL=7200  # 2 hours (less frequent)
KEEP_STATS_DAYS=7  # Shorter retention

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
SUPPORT_LINK=https://t.me/your_support_channel
GROUP_LINK=https://t.me/your_group
HELP_LINK=https://your-help-documentation.com

# API Keys (optional)
TMDB_API_KEY=
OMDB_API_KEY=

# =============================================================================
# RENDER.COM SPECIFIC CONFIGURATION
# =============================================================================
ENVIRONMENT=production
DEBUG=false

# Render.com specific settings
PORT=10000  # Render.com default port
HOST=0.0.0.0

# Performance optimizations for free tier
ENABLE_DEBUG_COMMANDS=false
MOCK_EXTERNAL_SERVICES=false

# Memory optimization
PYTHON_UNBUFFERED=1
PYTHONPATH=/opt/render/project/src
