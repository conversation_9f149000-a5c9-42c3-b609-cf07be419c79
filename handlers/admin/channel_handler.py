"""
Channel management handler for the Movie Management Bot.
Handles /channel commands for adding, removing, and managing channels.
"""

import logging
import re
from typing import Optional, List, Dict, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
from telegram.error import TelegramError

try:
    from config.admin_manager import is_admin, is_super_admin
except ImportError:
    async def is_admin(user_id): return False
    async def is_super_admin(user_id): return False

try:
    from config.channel_manager import (
        add_channel, remove_channel, get_all_channels,
        get_channel_info, toggle_channel_monitoring,
        channel_manager
    )
except ImportError:
    async def add_channel(*args, **kwargs): return False
    async def remove_channel(*args, **kwargs): return False
    async def get_all_channels(*args, **kwargs): return []
    async def get_channel_info(*args, **kwargs): return None
    async def toggle_channel_monitoring(*args, **kwargs): return False
    channel_manager = None

try:
    from utils.telegram_helpers import (
        verify_bot_admin_permissions,
        get_channel_info_from_telegram,
        extract_channel_identifier
    )
except ImportError:
    async def verify_bot_admin_permissions(*args, **kwargs): return False
    async def get_channel_info_from_telegram(*args, **kwargs): return None
    def extract_channel_identifier(text): return None

logger = logging.getLogger(__name__)


class ChannelHandler:
    """Handles channel management commands and operations."""

    def __init__(self):
        self.pending_operations = {}  # Track pending operations for confirmation

    async def handle_channel_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /channel command with subcommands."""
        try:
            user = update.effective_user
            if not user:
                return

            # Check admin permissions
            if not await is_admin(user.id):
                await update.message.reply_text(
                    "🔒 <b>Access Denied</b>\n\n"
                    "Only admins can manage channels.",
                    parse_mode=ParseMode.HTML
                )
                return

            # Parse command arguments
            args = context.args
            if not args:
                # Show channel list if no arguments
                await self._show_channel_list(update, context)
                return

            subcommand = args[0].lower()

            if subcommand == "add":
                await self._handle_add_channel(update, context, args[1:])
            elif subcommand == "remove":
                await self._handle_remove_channel(update, context, args[1:])
            elif subcommand == "list":
                await self._show_channel_list(update, context)
            elif subcommand == "info":
                await self._show_channel_info(update, context, args[1:])
            elif subcommand == "help":
                await self._show_help(update, context)
            else:
                await self._show_help(update, context)

        except Exception as e:
            logger.error(f"Error handling channel command: {e}")
            await update.message.reply_text(
                "🚫 <b>Error</b>\n\n"
                "Something went wrong. Please try again later.",
                parse_mode=ParseMode.HTML
            )

    async def _handle_add_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE, args: List[str]) -> None:
        """Handle channel addition."""
        if not args:
            await update.message.reply_text(
                "📝 <b>Add Channel</b>\n\n"
                "<b>Usage:</b> <code>/channel add @channel_username</code>\n"
                "<b>Or:</b> <code>/channel add -1001234567890</code>\n\n"
                "<b>Note:</b> Make sure the bot has admin rights in the channel first!",
                parse_mode=ParseMode.HTML
            )
            return

        channel_identifier = args[0]
        user_id = update.effective_user.id

        # Extract channel ID/username
        channel_info = extract_channel_identifier(channel_identifier)
        if not channel_info:
            await update.message.reply_text(
                "❌ <b>Invalid Channel</b>\n\n"
                "Please provide a valid channel username (@channel) or ID (-1001234567890).",
                parse_mode=ParseMode.HTML
            )
            return

        # Send processing message
        processing_msg = await update.message.reply_text(
            "⏳ <b>Adding Channel...</b>\n\n"
            "🔍 Verifying bot permissions...",
            parse_mode=ParseMode.HTML
        )

        try:
            # Get channel information from Telegram
            logger.info(f"Step 1: Getting channel info for {channel_info}")
            telegram_info = await get_channel_info_from_telegram(context.bot, channel_info)
            logger.info(f"Step 1 result: {telegram_info is not None}")

            if not telegram_info:
                await processing_msg.edit_text(
                    "❌ <b>Channel Not Found</b>\n\n"
                    "Could not access the channel. Make sure:\n"
                    "• The channel exists\n"
                    "• The bot has been added to the channel\n"
                    "• The channel username/ID is correct",
                    parse_mode=ParseMode.HTML
                )
                return

            # Verify bot admin permissions
            logger.info(f"Step 2: Verifying bot permissions for {telegram_info['channel_id']}")
            has_permissions = await verify_bot_admin_permissions(context.bot, telegram_info['channel_id'])
            logger.info(f"Step 2 result: {has_permissions}")

            if not has_permissions:
                await processing_msg.edit_text(
                    "🔒 <b>Insufficient Permissions</b>\n\n"
                    "The bot needs admin rights in the channel to monitor it.\n\n"
                    "<b>Please:</b>\n"
                    "1. Add the bot to the channel\n"
                    "2. Give it admin permissions\n"
                    "3. Try adding the channel again",
                    parse_mode=ParseMode.HTML
                )
                return

            # Add channel to database
            logger.info(f"Step 3: Adding channel to database")
            logger.info(f"Channel ID: {telegram_info['channel_id']}")
            logger.info(f"Channel name: {telegram_info.get('title')}")
            logger.info(f"User ID: {user_id}")

            try:
                logger.info("Step 3a: About to call add_channel function")
                success = await add_channel(
                    channel_id=telegram_info['channel_id'],
                    channel_username=telegram_info.get('username'),
                    channel_name=telegram_info.get('title'),
                    added_by=user_id
                )
                logger.info(f"Step 3b: add_channel returned: {success} (type: {type(success)})")
            except Exception as add_error:
                logger.error(f"Step 3c: Exception in add_channel: {add_error}")
                logger.error(f"Step 3c: Exception type: {type(add_error)}")
                import traceback
                logger.error(f"Step 3c: Traceback: {traceback.format_exc()}")
                success = False

            if success == True:
                await processing_msg.edit_text(
                    f"✅ <b>Channel Added Successfully!</b>\n\n"
                    f"📺 <b>Channel:</b> {telegram_info.get('title', 'Unknown')}\n"
                    f"🆔 <b>ID:</b> <code>{telegram_info['channel_id']}</code>\n"
                    f"👤 <b>Username:</b> {telegram_info.get('username', 'None')}\n\n"
                    f"⚠️ <b>Note:</b> Monitoring and search are disabled by default.\n"
                    f"Use <code>/channel</code> to enable them.",
                    parse_mode=ParseMode.HTML,
                    reply_markup=InlineKeyboardMarkup([[
                        InlineKeyboardButton("📺 Manage Channels", callback_data="manage_channels")
                    ]])
                )
            elif success == "already_exists":
                await processing_msg.edit_text(
                    f"⚠️ <b>Channel Already Added</b>\n\n"
                    f"📺 <b>Channel:</b> {telegram_info.get('title', 'Unknown')}\n"
                    f"🆔 <b>ID:</b> <code>{telegram_info['channel_id']}</code>\n\n"
                    f"This channel is already in your monitoring list.\n"
                    f"Use <code>/channel</code> to manage its settings.",
                    parse_mode=ParseMode.HTML,
                    reply_markup=InlineKeyboardMarkup([[
                        InlineKeyboardButton("📺 Manage Channels", callback_data="manage_channels")
                    ]])
                )
            else:
                await processing_msg.edit_text(
                    "❌ <b>Failed to Add Channel</b>\n\n"
                    "There was a database error while adding the channel.\n"
                    "Please try again later or contact support.",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            logger.error(f"Error adding channel {channel_identifier}: {e}")
            await processing_msg.edit_text(
                "🚫 <b>Error Adding Channel</b>\n\n"
                "An unexpected error occurred. Please try again later.",
                parse_mode=ParseMode.HTML
            )

    async def _handle_remove_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE, args: List[str]) -> None:
        """Handle channel removal."""
        if not args:
            await update.message.reply_text(
                "📝 <b>Remove Channel</b>\n\n"
                "<b>Usage:</b> <code>/channel remove @channel_username</code>\n"
                "<b>Or:</b> <code>/channel remove -1001234567890</code>",
                parse_mode=ParseMode.HTML
            )
            return

        channel_identifier = args[0]
        user_id = update.effective_user.id

        # Extract channel ID/username
        channel_info = extract_channel_identifier(channel_identifier)
        if not channel_info:
            await update.message.reply_text(
                "❌ <b>Invalid Channel</b>\n\n"
                "Please provide a valid channel username (@channel) or ID (-1001234567890).",
                parse_mode=ParseMode.HTML
            )
            return

        # Get channel info from database
        db_channel = await get_channel_info(channel_info['channel_id'])
        if not db_channel:
            await update.message.reply_text(
                "❌ <b>Channel Not Found</b>\n\n"
                "This channel is not in the monitoring list.\n"
                "Use <code>/channel list</code> to see monitored channels.",
                parse_mode=ParseMode.HTML
            )
            return

        # Show confirmation dialog
        await update.message.reply_text(
            f"⚠️ <b>Confirm Channel Removal</b>\n\n"
            f"📺 <b>Channel:</b> {db_channel.get('channel_name', 'Unknown')}\n"
            f"🆔 <b>ID:</b> <code>{db_channel['channel_id']}</code>\n\n"
            f"❗ <b>Warning:</b> This will remove the channel from monitoring and "
            f"delete all indexed content. This action cannot be undone.\n\n"
            f"Are you sure you want to continue?",
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("✅ Yes, Remove", callback_data=f"confirm_remove_{db_channel['channel_id']}"),
                    InlineKeyboardButton("❌ Cancel", callback_data="cancel_remove")
                ]
            ])
        )

    async def _show_channel_list(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show list of all channels with management options."""
        logger.info("🔥 HANDLER: Getting channel list...")
        channels = await get_all_channels(include_inactive=False)
        logger.info(f"🔥 HANDLER: Retrieved {len(channels)} channels")

        if not channels:
            await update.message.reply_text(
                "📺 <b>Channel Management</b>\n\n"
                "No channels are currently being monitored.\n\n"
                "<b>To add a channel:</b>\n"
                "<code>/channel add @your_channel</code>",
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("📚 Help", callback_data="channel_help")
                ]])
            )
            return

        # Build channel list message
        message_text = "📺 <b>Channel Management</b>\n\n"

        for i, channel in enumerate(channels, 1):
            channel_name = channel.get('channel_name', 'Unknown Channel')
            channel_id = channel['channel_id']
            is_monitored = channel.get('is_monitored', False)
            is_searchable = channel.get('is_searchable', True)  # Default to True if not set

            # Status indicators
            monitor_status = "🟢" if is_monitored else "🔴"
            search_status = "🟢" if is_searchable else "🔴"

            message_text += (
                f"<b>{i}. {channel_name}</b>\n"
                f"   🆔 <code>{channel_id}</code>\n"
                f"   📊 Monitoring: {monitor_status} | Search: {search_status}\n\n"
            )

        # Build inline keyboard for channel management
        keyboard = []
        for channel in channels[:5]:  # Limit to 5 channels per page
            channel_name = channel.get('channel_name', f"Channel {channel['channel_id']}")
            if len(channel_name) > 20:
                channel_name = channel_name[:17] + "..."

            keyboard.append([
                InlineKeyboardButton(
                    f"⚙️ {channel_name}",
                    callback_data=f"manage_channel_{channel['channel_id']}"
                )
            ])

        # Add management buttons
        keyboard.extend([
            [
                InlineKeyboardButton("➕ Add Channel", callback_data="add_channel_prompt"),
                InlineKeyboardButton("🔄 Refresh", callback_data="refresh_channels")
            ],
            [
                InlineKeyboardButton("📚 Help", callback_data="channel_help")
            ]
        ])

        await update.message.reply_text(
            message_text,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )


    async def _show_channel_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE, args: List[str]) -> None:
        """Show detailed information about a specific channel."""
        if not args:
            await update.message.reply_text(
                "📝 <b>Channel Info</b>\n\n"
                "<b>Usage:</b> <code>/channel info @channel_username</code>\n"
                "<b>Or:</b> <code>/channel info -1001234567890</code>",
                parse_mode=ParseMode.HTML
            )
            return

        channel_identifier = args[0]

        # Extract channel ID/username
        channel_info = extract_channel_identifier(channel_identifier)
        if not channel_info:
            await update.message.reply_text(
                "❌ <b>Invalid Channel</b>\n\n"
                "Please provide a valid channel username (@channel) or ID (-1001234567890).",
                parse_mode=ParseMode.HTML
            )
            return

        # Get channel info from database
        db_channel = await get_channel_info(channel_info.get('channel_id') or channel_info.get('username'))
        if not db_channel:
            await update.message.reply_text(
                "❌ <b>Channel Not Found</b>\n\n"
                "This channel is not in the monitoring list.\n"
                "Use <code>/channel list</code> to see monitored channels.",
                parse_mode=ParseMode.HTML
            )
            return

        # Get current permissions from Telegram
        try:
            from utils.telegram_helpers import get_bot_permissions_in_channel, format_channel_status_message
            permissions = await get_bot_permissions_in_channel(context.bot, db_channel['channel_id'])
            message_text = await format_channel_status_message(db_channel, permissions)
        except ImportError:
            message_text = f"📺 <b>{db_channel.get('channel_name', 'Unknown Channel')}</b>\n\n"
            message_text += f"🆔 <b>ID:</b> <code>{db_channel['channel_id']}</code>\n"
            message_text += f"📊 <b>Status:</b> {'🟢 Active' if db_channel.get('is_monitored') else '🔴 Inactive'}"

        await update.message.reply_text(
            message_text,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("⚙️ Manage", callback_data=f"manage_channel_{db_channel['channel_id']}")
            ]])
        )

    async def _show_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show channel management help."""
        help_text = """📚 <b>Channel Management Help</b>

<b>🔧 Available Commands:</b>

<code>/channel</code> - Show all channels
<code>/channel add @channel</code> - Add channel to monitoring
<code>/channel remove @channel</code> - Remove channel
<code>/channel list</code> - List all channels
<code>/channel info @channel</code> - Show channel details
<code>/channel help</code> - Show this help

<b>📋 Channel States:</b>

🟢 <b>Monitoring ON:</b> Bot indexes new content
🔴 <b>Monitoring OFF:</b> Bot ignores new content

🟢 <b>Search ON:</b> Content appears in search results
🔴 <b>Search OFF:</b> Content hidden from search

<b>⚠️ Requirements:</b>
• Bot must be added to the channel
• Bot needs admin permissions
• Only admins can manage channels

<b>💡 Tips:</b>
• New channels have monitoring/search disabled by default
• Use the channel list to toggle settings
• Removing a channel deletes all indexed content"""

        await update.message.reply_text(
            help_text,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("📺 View Channels", callback_data="manage_channels")
            ]])
        )

    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle callback queries from channel management buttons."""
        query = update.callback_query
        await query.answer()

        callback_data = query.data
        user = update.effective_user

        logger.info(f"🔥 CALLBACK: Channel handler received callback: {callback_data}")
        logger.info(f"🔥 CALLBACK: User: {user.id} ({user.username})")

        # Check admin permissions
        if not await is_admin(user.id):
            await query.edit_message_text(
                "🔒 <b>Access Denied</b>\n\n"
                "Only admins can manage channels.",
                parse_mode=ParseMode.HTML
            )
            return

        if callback_data == "manage_channels":
            await self._show_channel_list_callback(query, context)
        elif callback_data == "add_channel_prompt":
            await self._show_add_channel_prompt(query, context)
        elif callback_data == "refresh_channels":
            await self._show_channel_list_callback(query, context)
        elif callback_data == "channel_help":
            await self._show_help_callback(query, context)
        elif callback_data.startswith("manage_channel_"):
            channel_id = int(callback_data.split("_")[-1])
            await self._show_channel_management(query, context, channel_id)
        elif callback_data.startswith("toggle_monitoring_"):
            channel_id = int(callback_data.split("_")[-1])
            await self._toggle_channel_monitoring(query, context, channel_id)
        elif callback_data.startswith("toggle_search_"):
            channel_id = int(callback_data.split("_")[-1])
            await self._toggle_channel_search(query, context, channel_id)
        elif callback_data.startswith("ask_remove_"):
            channel_id = int(callback_data.split("_")[-1])
            await self._ask_channel_removal_confirmation(query, context, channel_id)
        elif callback_data.startswith("confirm_remove_"):
            channel_id = int(callback_data.split("_")[-1])
            await self._execute_channel_removal(query, context, channel_id)
        elif callback_data == "cancel_remove":
            await query.edit_message_text(
                "❌ <b>Channel Removal Cancelled</b>\n\n"
                "The channel was not removed.",
                parse_mode=ParseMode.HTML
            )
        elif callback_data.startswith("rescan_channel_"):
            channel_id = int(callback_data.split("_")[-1])
            await self._handle_rescan_channel(query, context, channel_id)
        elif callback_data.startswith("confirm_rescan_"):
            channel_id = int(callback_data.split("_")[-1])
            await self._execute_channel_rescan(query, context, channel_id)

    async def _show_channel_list_callback(self, query, context):
        """Show channel list via callback query."""
        logger.info("🔥 HANDLER_CALLBACK: Getting channel list...")
        channels = await get_all_channels(include_inactive=False)
        logger.info(f"🔥 HANDLER_CALLBACK: Retrieved {len(channels)} channels")

        if not channels:
            await query.edit_message_text(
                "📺 <b>Channel Management</b>\n\n"
                "No channels are currently being monitored.\n\n"
                "<b>To add a channel:</b>\n"
                "<code>/channel add @your_channel</code>",
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("📚 Help", callback_data="channel_help")
                ]])
            )
            return

        # Build channel list message
        message_text = "📺 <b>Channel Management</b>\n\n"

        for i, channel in enumerate(channels, 1):
            channel_name = channel.get('channel_name', 'Unknown Channel')
            channel_id = channel['channel_id']
            is_monitored = channel.get('is_monitored', False)
            is_searchable = channel.get('is_searchable', True)

            # Status indicators
            monitor_status = "🟢" if is_monitored else "🔴"
            search_status = "🟢" if is_searchable else "🔴"

            message_text += (
                f"<b>{i}. {channel_name}</b>\n"
                f"   🆔 <code>{channel_id}</code>\n"
                f"   📊 Monitoring: {monitor_status} | Search: {search_status}\n\n"
            )

        # Build inline keyboard
        keyboard = []
        for channel in channels[:5]:
            channel_name = channel.get('channel_name', f"Channel {channel['channel_id']}")
            if len(channel_name) > 20:
                channel_name = channel_name[:17] + "..."

            keyboard.append([
                InlineKeyboardButton(
                    f"⚙️ {channel_name}",
                    callback_data=f"manage_channel_{channel['channel_id']}"
                )
            ])

        keyboard.extend([
            [
                InlineKeyboardButton("➕ Add Channel", callback_data="add_channel_prompt"),
                InlineKeyboardButton("🔄 Refresh", callback_data="refresh_channels")
            ],
            [
                InlineKeyboardButton("📚 Help", callback_data="channel_help")
            ]
        ])

        try:
            await query.edit_message_text(
                message_text,
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except Exception as e:
            # Handle "message not modified" error for refresh button
            if "Message is not modified" in str(e):
                await query.answer("✅ Channel list is already up to date!", show_alert=False)
            else:
                logger.error(f"Error updating channel list: {e}")
                await query.answer("❌ Error refreshing channel list", show_alert=True)


    async def _show_add_channel_prompt(self, query, context):
        """Show add channel prompt."""
        await query.edit_message_text(
            "➕ <b>Add New Channel</b>\n\n"
            "To add a channel to monitoring:\n\n"
            "<b>1.</b> Add the bot to your channel\n"
            "<b>2.</b> Give the bot admin permissions\n"
            "<b>3.</b> Use the command:\n"
            "<code>/channel add @your_channel</code>\n\n"
            "<b>Note:</b> You can also use the channel ID instead of username.",
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("🔙 Back to Channels", callback_data="manage_channels")
            ]])
        )

    async def _show_help_callback(self, query, context):
        """Show help via callback query."""
        help_text = """📚 <b>Channel Management Help</b>

<b>🔧 Available Commands:</b>

<code>/channel</code> - Show all channels
<code>/channel add @channel</code> - Add channel
<code>/channel remove @channel</code> - Remove channel
<code>/channel list</code> - List all channels
<code>/channel info @channel</code> - Channel details

<b>📋 Channel States:</b>

🟢 <b>Monitoring ON:</b> Bot indexes new content
🔴 <b>Monitoring OFF:</b> Bot ignores new content

🟢 <b>Search ON:</b> Content appears in search
🔴 <b>Search OFF:</b> Content hidden from search

<b>⚠️ Requirements:</b>
• Bot must be added to the channel
• Bot needs admin permissions
• Only admins can manage channels"""

        await query.edit_message_text(
            help_text,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("🔙 Back to Channels", callback_data="manage_channels")
            ]])
        )

    async def _show_channel_management(self, query, context, channel_id: int):
        """Show individual channel management options."""
        # Get channel info
        channel = await get_channel_info(channel_id)
        if not channel:
            await query.edit_message_text(
                "❌ <b>Channel Not Found</b>\n\n"
                "This channel is no longer available.",
                parse_mode=ParseMode.HTML
            )
            return

        channel_name = channel.get('channel_name', 'Unknown Channel')
        is_monitored = channel.get('is_monitored', False)
        is_searchable = channel.get('is_searchable', True)

        # Status indicators
        monitor_status = "🟢 ON" if is_monitored else "🔴 OFF"
        search_status = "🟢 ON" if is_searchable else "🔴 OFF"

        message_text = f"⚙️ <b>Manage Channel</b>\n\n"
        message_text += f"📺 <b>{channel_name}</b>\n"
        message_text += f"🆔 <code>{channel_id}</code>\n\n"
        message_text += f"📊 <b>Current Status:</b>\n"
        message_text += f"• Monitoring: {monitor_status}\n"
        message_text += f"• Search: {search_status}\n\n"

        # Build keyboard
        keyboard = [
            [
                InlineKeyboardButton(
                    f"📊 {'Disable' if is_monitored else 'Enable'} Monitoring",
                    callback_data=f"toggle_monitoring_{channel_id}"
                )
            ],
            [
                InlineKeyboardButton(
                    f"🔍 {'Disable' if is_searchable else 'Enable'} Search",
                    callback_data=f"toggle_search_{channel_id}"
                )
            ],
            [
                InlineKeyboardButton(
                    "🔄 Re-scan Channel",
                    callback_data=f"rescan_channel_{channel_id}"
                )
            ]
        ]

        # Add forget option only if both monitoring and search are off
        if not is_monitored and not is_searchable:
            keyboard.append([
                InlineKeyboardButton(
                    "🗑️ Forget Channel",
                    callback_data=f"ask_remove_{channel_id}"
                )
            ])

        keyboard.append([
            InlineKeyboardButton("🔙 Back to Channels", callback_data="manage_channels")
        ])

        await query.edit_message_text(
            message_text,
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    async def _toggle_channel_monitoring(self, query, context, channel_id: int):
        """Toggle channel monitoring status."""
        # Get current status
        channel = await get_channel_info(channel_id)
        if not channel:
            await query.answer("❌ Channel not found", show_alert=True)
            return

        current_status = channel.get('is_monitored', False)
        new_status = not current_status

        # Update in database
        success = await toggle_channel_monitoring(channel_id, new_status, query.from_user.id)

        if success:
            status_text = "enabled" if new_status else "disabled"
            await query.answer(f"✅ Monitoring {status_text}", show_alert=False)
            # Refresh the management view
            await self._show_channel_management(query, context, channel_id)
        else:
            await query.answer("❌ Failed to update monitoring status", show_alert=True)

    async def _toggle_channel_search(self, query, context, channel_id: int):
        """Toggle channel search status."""
        # Get current status
        channel = await get_channel_info(channel_id)
        if not channel:
            await query.answer("❌ Channel not found", show_alert=True)
            return

        current_status = channel.get('is_searchable', True)
        new_status = not current_status

        # Update in database
        if channel_manager:
            success = await channel_manager.update_channel_settings(
                channel_id,
                {'is_searchable': new_status},
                query.from_user.id
            )
        else:
            success = False

        if success:
            status_text = "enabled" if new_status else "disabled"
            await query.answer(f"✅ Search {status_text}", show_alert=False)
            # Refresh the management view
            await self._show_channel_management(query, context, channel_id)
        else:
            await query.answer("❌ Failed to update search status", show_alert=True)

    async def _ask_channel_removal_confirmation(self, query, context, channel_id: int):
        """Show confirmation dialog for channel removal."""
        # Get channel info for confirmation
        channel = await get_channel_info(channel_id)
        if not channel:
            await query.edit_message_text(
                "❌ <b>Channel Not Found</b>\n\n"
                "This channel is no longer available.",
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 Back to Channels", callback_data="manage_channels")
                ]])
            )
            return

        channel_name = channel.get('channel_name', 'Unknown Channel')
        await query.edit_message_text(
            f"⚠️ <b>Confirm Channel Removal</b>\n\n"
            f"📺 <b>Channel:</b> {channel_name}\n"
            f"🆔 <b>ID:</b> <code>{channel_id}</code>\n\n"
            f"🚨 <b>Warning:</b> This will permanently remove the channel from monitoring and "
            f"delete all indexed content. This action cannot be undone.\n\n"
            f"Are you sure you want to continue?",
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("✅ Yes, Remove", callback_data=f"confirm_remove_{channel_id}"),
                    InlineKeyboardButton("❌ Cancel", callback_data=f"manage_channel_{channel_id}")
                ]
            ])
        )

    async def _execute_channel_removal(self, query, context, channel_id: int):
        """Execute the actual channel removal after confirmation."""
        user_id = query.from_user.id

        logger.info(f"🔥 REMOVE: Starting removal of channel {channel_id} by user {user_id}")

        # Get channel info for confirmation
        channel = await get_channel_info(channel_id)
        if not channel:
            logger.warning(f"🔥 REMOVE: Channel {channel_id} not found in database")
            await query.edit_message_text(
                "❌ <b>Channel Not Found</b>\n\n"
                "This channel is no longer available.",
                parse_mode=ParseMode.HTML
            )
            return

        logger.info(f"🔥 REMOVE: Channel found: {channel.get('channel_name', 'Unknown')}")
        logger.info(f"🔥 REMOVE: Calling remove_channel function...")

        # Remove channel
        success = await remove_channel(channel_id, user_id)

        logger.info(f"🔥 REMOVE: remove_channel returned: {success} (type: {type(success)})")

        if success:
            channel_name = channel.get('channel_name', 'Unknown Channel')
            await query.edit_message_text(
                f"✅ <b>Channel Removed Successfully</b>\n\n"
                f"📺 <b>{channel_name}</b> has been removed from monitoring.\n"
                f"All indexed content has been deleted.\n\n"
                f"You can add it back anytime using:\n"
                f"<code>/channel add {channel_id}</code>",
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("📺 View Channels", callback_data="manage_channels")
                ]])
            )
        else:
            await query.edit_message_text(
                "❌ <b>Failed to Remove Channel</b>\n\n"
                "An error occurred while removing the channel. Please try again.",
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 Back to Channels", callback_data="manage_channels")
                ]])
            )

    async def _handle_rescan_channel(self, query, context, channel_id: int):
        """Handle channel re-scan request."""
        # Get channel info
        channel = await get_channel_info(channel_id)
        if not channel:
            await query.edit_message_text(
                "❌ <b>Channel Not Found</b>\n\n"
                "This channel is no longer available.",
                parse_mode=ParseMode.HTML
            )
            return

        channel_name = channel.get('channel_name', 'Unknown Channel')

        # Show confirmation dialog for re-scan
        await query.edit_message_text(
            f"🔄 <b>Confirm Channel Re-scan</b>\n\n"
            f"📺 <b>Channel:</b> {channel_name}\n"
            f"🆔 <b>ID:</b> <code>{channel_id}</code>\n\n"
            f"⚠️ <b>Warning:</b> This will:\n"
            f"• Turn off monitoring temporarily\n"
            f"• Delete all existing indexed content\n"
            f"• Re-scan the channel from scratch\n"
            f"• Turn monitoring back on\n\n"
            f"This process may take several minutes.\n\n"
            f"Are you sure you want to continue?",
            parse_mode=ParseMode.HTML,
            reply_markup=InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("✅ Yes, Re-scan", callback_data=f"confirm_rescan_{channel_id}"),
                    InlineKeyboardButton("❌ Cancel", callback_data=f"manage_channel_{channel_id}")
                ]
            ])
        )

    async def _execute_channel_rescan(self, query, context, channel_id: int):
        """Execute the actual channel re-scan after confirmation."""
        user_id = query.from_user.id

        logger.info(f"🔄 RESCAN: Starting re-scan of channel {channel_id} by user {user_id}")

        # Get channel info
        channel = await get_channel_info(channel_id)
        if not channel:
            logger.warning(f"🔄 RESCAN: Channel {channel_id} not found in database")
            await query.edit_message_text(
                "❌ <b>Channel Not Found</b>\n\n"
                "This channel is no longer available.",
                parse_mode=ParseMode.HTML
            )
            return

        channel_name = channel.get('channel_name', 'Unknown Channel')
        logger.info(f"🔄 RESCAN: Channel found: {channel_name}")

        # Start re-scan process
        try:
            # Import indexing service
            try:
                from services.indexing_service import IndexingService
                indexing_service = IndexingService()
            except ImportError:
                logger.error("🔄 RESCAN: Indexing service not available")
                await query.edit_message_text(
                    "❌ <b>Indexing Service Unavailable</b>\n\n"
                    "The indexing service is not properly configured.\n"
                    "Please contact support.",
                    parse_mode=ParseMode.HTML
                )
                return

            # Send initial status message
            status_msg = await query.edit_message_text(
                f"🔄 <b>Re-scanning Channel</b>\n\n"
                f"📺 <b>{channel_name}</b>\n"
                f"🆔 <code>{channel_id}</code>\n\n"
                f"⏳ <b>Status:</b> Starting re-scan process...\n\n"
                f"Please wait, this may take several minutes.",
                parse_mode=ParseMode.HTML
            )

            # Execute re-scan
            success = await indexing_service.rescan_channel(channel_id, user_id)

            if success:
                await status_msg.edit_text(
                    f"✅ <b>Channel Re-scan Completed</b>\n\n"
                    f"📺 <b>{channel_name}</b>\n"
                    f"🆔 <code>{channel_id}</code>\n\n"
                    f"🎉 The channel has been successfully re-scanned!\n"
                    f"All content has been re-indexed from scratch.\n\n"
                    f"Monitoring has been re-enabled.",
                    parse_mode=ParseMode.HTML,
                    reply_markup=InlineKeyboardMarkup([[
                        InlineKeyboardButton("📺 View Channels", callback_data="manage_channels")
                    ]])
                )
            else:
                await status_msg.edit_text(
                    f"❌ <b>Re-scan Failed</b>\n\n"
                    f"📺 <b>{channel_name}</b>\n"
                    f"🆔 <code>{channel_id}</code>\n\n"
                    f"An error occurred during the re-scan process.\n"
                    f"Please try again or contact support.\n\n"
                    f"Monitoring status has been restored.",
                    parse_mode=ParseMode.HTML,
                    reply_markup=InlineKeyboardMarkup([[
                        InlineKeyboardButton("🔄 Try Again", callback_data=f"rescan_channel_{channel_id}"),
                        InlineKeyboardButton("📺 View Channels", callback_data="manage_channels")
                    ]])
                )

        except Exception as e:
            logger.error(f"🔄 RESCAN: Error during re-scan of channel {channel_id}: {e}")
            await query.edit_message_text(
                f"❌ <b>Re-scan Error</b>\n\n"
                f"📺 <b>{channel_name}</b>\n"
                f"🆔 <code>{channel_id}</code>\n\n"
                f"An unexpected error occurred during the re-scan.\n"
                f"Please try again or contact support.",
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔄 Try Again", callback_data=f"rescan_channel_{channel_id}"),
                    InlineKeyboardButton("📺 View Channels", callback_data="manage_channels")
                ]])
            )


# Global handler instance
channel_handler = ChannelHandler()