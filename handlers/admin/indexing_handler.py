"""
Indexing handler for the Cognito Movie Management Bot.
Handles /index commands for manual indexing operations.
"""

import logging
from typing import Dict, Any
from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import ContextTypes

from config.admin_manager import is_admin
from services.indexing_service import get_indexing_service
from config.channel_manager import get_all_channels, get_channel_info

logger = logging.getLogger(__name__)


class IndexingHandler:
    """Handles indexing-related commands and operations."""

    def __init__(self):
        self.indexing_service = get_indexing_service()

    async def handle_index_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Handle /index command for overall indexing operations.

        Usage:
        /index - Index all channels
        /index -r channel_id - Re-index specific channel
        """
        user = update.effective_user
        if not user:
            return

        # Check admin permissions
        if not await is_admin(user.id):
            await update.message.reply_text(
                "🚫 **Access Denied**\n\n"
                "Only admins can use indexing commands.\n"
                "Contact an administrator for access."
            )
            return

        args = context.args or []

        if not args:
            # Index all channels
            await self._handle_index_all(update)
        elif len(args) == 2 and args[0] == '-r':
            # Re-index specific channel
            try:
                channel_id = int(args[1])
                await self._handle_reindex_channel(update, channel_id)
            except ValueError:
                await update.message.reply_text(
                    "❌ **Invalid Channel ID**\n\n"
                    "Please provide a valid numeric channel ID.\n"
                    "Example: `/index -r -1001234567890`"
                )
        else:
            await self._show_index_help(update)

    async def _handle_index_all(self, update: Update) -> None:
        """Handle indexing all channels."""
        if not self.indexing_service:
            await update.message.reply_text(
                "❌ **Indexing Service Unavailable**\n\n"
                "The indexing service is not initialized."
            )
            return

        # Send initial message
        status_message = await update.message.reply_text(
            "🔄 **Starting Indexing Process**\n\n"
            "Scanning all monitored channels for new movies...\n"
            "This may take a few minutes."
        )

        try:
            # Start indexing
            results = await self.indexing_service.index_all_channels()

            # Format results
            summary = self._format_indexing_results(results)

            await status_message.edit_text(
                f"✅ **Indexing Completed**\n\n{summary}",
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error during indexing: {e}")
            await status_message.edit_text(
                "❌ **Indexing Failed**\n\n"
                f"An error occurred during indexing:\n`{str(e)}`\n\n"
                "Please check the logs for more details."
            )

    async def _handle_reindex_channel(self, update: Update, channel_id: int) -> None:
        """Handle re-indexing a specific channel with confirmation."""
        # Check if channel exists
        channel_info = await get_channel_info(channel_id)
        if not channel_info:
            await update.message.reply_text(
                f"❌ **Channel Not Found**\n\n"
                f"Channel `{channel_id}` is not in the database.\n"
                "Use `/channel list` to see available channels."
            )
            return

        # Show confirmation prompt
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("✅ Confirm Re-index", callback_data=f"confirm_reindex_{channel_id}"),
                InlineKeyboardButton("❌ Cancel", callback_data="cancel_reindex")
            ]
        ])

        channel_name = channel_info.get('channel_username', f"ID: {channel_id}")

        await update.message.reply_text(
            f"⚠️ **Re-index Channel Confirmation**\n\n"
            f"**Channel:** {channel_name}\n"
            f"**ID:** `{channel_id}`\n\n"
            f"**Warning:** This will:\n"
            f"• Turn off monitoring temporarily\n"
            f"• Delete all existing indexed data\n"
            f"• Re-scan from scratch\n"
            f"• Turn monitoring back on\n\n"
            f"This action cannot be undone. Are you sure?",
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    async def handle_reindex_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle re-index confirmation callback."""
        query = update.callback_query
        await query.answer()

        user = update.effective_user
        if not user or not await is_admin(user.id):
            await query.edit_message_text(
                "🚫 **Access Denied**\n\nOnly admins can perform re-indexing."
            )
            return

        callback_data = query.data

        if callback_data == "cancel_reindex":
            await query.edit_message_text(
                "❌ **Re-index Cancelled**\n\n"
                "No changes were made."
            )
            return

        if callback_data.startswith("confirm_reindex_"):
            try:
                channel_id = int(callback_data.split("_")[-1])

                # Verify channel exists
                channel_info = await get_channel_info(channel_id)
                if not channel_info:
                    await query.edit_message_text(
                        "❌ **Channel Not Found**\n\n"
                        "The channel no longer exists in the database."
                    )
                    return

                # Start re-indexing
                await query.edit_message_text(
                    f"🔄 **Re-indexing Channel**\n\n"
                    f"Channel: {channel_info.get('channel_username', f'ID: {channel_id}')}\n"
                    f"Status: Processing...\n\n"
                    f"This may take several minutes."
                )

                if not self.indexing_service:
                    await query.edit_message_text(
                        "❌ **Indexing Service Unavailable**\n\n"
                        "The indexing service is not initialized."
                    )
                    return

                # Perform re-indexing
                stats = await self.indexing_service.reindex_channel(channel_id)

                # Format results
                result_text = self._format_channel_reindex_results(channel_info, stats)

                await query.edit_message_text(
                    result_text,
                    parse_mode='Markdown'
                )

            except ValueError:
                await query.edit_message_text(
                    "❌ **Invalid Channel ID**\n\n"
                    "The callback data is corrupted."
                )
            except Exception as e:
                logger.error(f"Error during re-indexing: {e}")
                await query.edit_message_text(
                    "❌ **Re-indexing Failed**\n\n"
                    f"An error occurred:\n`{str(e)}`\n\n"
                    "Please check the logs for more details."
                )

    async def _show_index_help(self, update: Update) -> None:
        """Show help for index commands."""
        help_text = (
            "📊 **Indexing Commands Help**\n\n"
            "**Available Commands:**\n"
            "• `/index` - Index all monitored channels\n"
            "• `/index -r <channel_id>` - Re-index specific channel\n\n"
            "**Examples:**\n"
            "• `/index`\n"
            "• `/index -r -1001234567890`\n\n"
            "**Notes:**\n"
            "• Re-indexing will delete existing data\n"
            "• Only admins can use these commands\n"
            "• Indexing runs automatically in background"
        )

        await update.message.reply_text(help_text, parse_mode='Markdown')

    def _format_indexing_results(self, results: Dict[str, Dict[str, int]]) -> str:
        """Format indexing results for display."""
        if not results:
            return "No channels were indexed."

        lines = []
        total_files = 0
        total_indexed = 0
        total_errors = 0

        for channel_id, stats in results.items():
            files_found = stats.get('files_found', 0)
            files_indexed = stats.get('files_indexed', 0)
            errors = stats.get('errors', 0)

            total_files += files_found
            total_indexed += files_indexed
            total_errors += errors

            if files_indexed > 0 or errors > 0:
                lines.append(f"• `{channel_id}`: {files_indexed} indexed, {errors} errors")

        summary = (
            f"**Summary:**\n"
            f"• Total files found: {total_files}\n"
            f"• New files indexed: {total_indexed}\n"
            f"• Errors: {total_errors}\n\n"
        )

        if lines:
            summary += "**Channel Details:**\n" + "\n".join(lines)

        return summary

    def _format_channel_reindex_results(self, channel_info: Dict[str, Any], stats: Dict[str, int]) -> str:
        """Format re-indexing results for a specific channel."""
        channel_name = channel_info.get('channel_username', f"ID: {channel_info['channel_id']}")

        result = (
            f"✅ **Re-indexing Completed**\n\n"
            f"**Channel:** {channel_name}\n"
            f"**ID:** `{channel_info['channel_id']}`\n\n"
            f"**Results:**\n"
            f"• Files found: {stats.get('files_found', 0)}\n"
            f"• Files indexed: {stats.get('files_indexed', 0)}\n"
            f"• Files skipped: {stats.get('files_skipped', 0)}\n"
            f"• Errors: {stats.get('errors', 0)}\n\n"
            f"Monitoring has been re-enabled for this channel."
        )

        return result


# Global instance
indexing_handler = IndexingHandler()


# Convenience functions for external use
async def handle_index_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle /index command."""
    await indexing_handler.handle_index_command(update, context)


async def handle_indexing_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle indexing-related callbacks."""
    await indexing_handler.handle_reindex_callback(update, context)
