version: "3.8"

services:
  # Main Bot Service
  bot:
    build: .
    container_name: media_bot
    restart: unless-stopped
    env_file:
      - .env.docker
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - redis
      # - elasticsearch  # Optional: Only if using Elasticsearch
    # Note: Redis and Elasticsearch are optional for free hosting
    networks:
      - bot_network
    ports:
      - "8000:8000" # Prometheus metrics port

  # Note: Using online MongoDB - no local database container needed

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: media_bot_redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - bot_network
    ports:
      - "6379:6379"

  # Elasticsearch for Search
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: media_bot_elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - bot_network
    ports:
      - "9200:9200"
      - "9300:9300"

  # Prometheus for Monitoring (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: media_bot_prometheus
    restart: unless-stopped
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--storage.tsdb.path=/prometheus"
      - "--web.console.libraries=/etc/prometheus/console_libraries"
      - "--web.console.templates=/etc/prometheus/consoles"
      - "--storage.tsdb.retention.time=200h"
      - "--web.enable-lifecycle"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - bot_network
    ports:
      - "9090:9090"
    profiles:
      - monitoring

  # Grafana for Visualization (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: media_bot_grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - bot_network
    ports:
      - "3000:3000"
    profiles:
      - monitoring

volumes:
  redis_data:
  elasticsearch_data:
  prometheus_data:
  grafana_data:

networks:
  bot_network:
    driver: bridge
