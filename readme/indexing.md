This read me file is a guide on what the indexing features does, and how to go about it.

- This feature will have its own branch `feature/indexing`, and will be completed once there is a thorough test and acceptance that everything works as expected, also merged with the main branch.
- What this feature does is to scan added channels (with monitoring set to true) for new contents (movie files), and index it into the database.
- The indexing process will be done in the background and automatically, in a polling manner, where it will listen to a channel change event (addition, renaming, deletion of contents, etc.) and update the database accordingly.
- This feature will have the bot command `/index` for the overall indexing.
- There will be an additional bot command `/index -r channelID` to re-scan a specific channel.
- The re-scan purpose is mostly done to correct channel data, especially when a particular channel has gone through several changes or update while monitoring is set to false.
- The re-scan channel process:
  - Turn off monitoring -> wipe indexed data -> index from scratch -> turn on monitoring.
- The re-scan command must come with a prompt to confirm this action.
- There will be a re-scan button attached to every channel listed in the channel manager.
- The re-scan button will trigger the `/index -r channelID` command.
- What to index:
  - Movie title
  - Movie year
  - Movie quality
  - Movie file size
  - Movie file extension
  - Movie file resolution
  - Movie file codec
  - Movie file audio codec
  - Movie file rip source
  - Movie file date added or modified
  - Movie file type (movie, series, etc.)
  - Movie file telegram link
- Most of the info, eg. (title, year, resolution, codec, rip, type, quality) will be extracted from the movie file description.
- Other info such as size, link, date added/modified, etc will come from telegram file api.
- Only Admins with rights can have access to the indexing feature.
