"""
Movie metadata extraction utilities for the Cognito bot.
Extracts movie information from filenames and Telegram API data.
"""

import re
import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)


class MovieMetadataExtractor:
    """Extracts movie metadata from filenames and file information."""

    # Common movie file extensions
    MOVIE_EXTENSIONS = {
        '.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v',
        '.3gp', '.mpg', '.mpeg', '.ts', '.mts', '.m2ts'
    }

    # Quality patterns
    QUALITY_PATTERNS = {
        '4k': re.compile(r'\b(4k|2160p|uhd)\b', re.IGNORECASE),
        '1080p': re.compile(r'\b(1080p|fullhd|fhd)\b', re.IGNORECASE),
        '720p': re.compile(r'\b(720p|hd)\b', re.IGNORECASE),
        '480p': re.compile(r'\b(480p|sd)\b', re.IGNORECASE),
        '360p': re.compile(r'\b(360p)\b', re.IGNORECASE),
    }

    # Source patterns
    SOURCE_PATTERNS = {
        'bluray': re.compile(r'\b(blu.?ray|bd|br)\b', re.IGNORECASE),
        'web-dl': re.compile(r'\b(web.?dl|webrip|netflix|amazon|hulu)\b', re.IGNORECASE),
        'hdtv': re.compile(r'\b(hdtv)\b', re.IGNORECASE),
        'dvd': re.compile(r'\b(dvd|dvdrip)\b', re.IGNORECASE),
        'cam': re.compile(r'\b(cam|ts|hdts)\b', re.IGNORECASE),
        'screener': re.compile(r'\b(screener|scr|dvdscr)\b', re.IGNORECASE),
    }

    # Codec patterns
    CODEC_PATTERNS = {
        'x264': re.compile(r'\b(x264|h264)\b', re.IGNORECASE),
        'x265': re.compile(r'\b(x265|h265|hevc)\b', re.IGNORECASE),
        'avc': re.compile(r'\b(avc)\b', re.IGNORECASE),
        'divx': re.compile(r'\b(divx)\b', re.IGNORECASE),
        'xvid': re.compile(r'\b(xvid)\b', re.IGNORECASE),
    }

    # Audio codec patterns
    AUDIO_PATTERNS = {
        'aac': re.compile(r'\b(aac)\b', re.IGNORECASE),
        'ac3': re.compile(r'\b(ac3|dd5\.1|dd)\b', re.IGNORECASE),
        'dts': re.compile(r'\b(dts|dts.hd)\b', re.IGNORECASE),
        'mp3': re.compile(r'\b(mp3)\b', re.IGNORECASE),
        'flac': re.compile(r'\b(flac)\b', re.IGNORECASE),
    }

    def __init__(self):
        self.year_pattern = re.compile(r'\b(19|20)\d{2}\b')
        self.episode_pattern = re.compile(r'\b[Ss]\d+[Ee]\d+\b')
        self.season_pattern = re.compile(r'\b[Ss]eason\s*\d+\b', re.IGNORECASE)

    def extract_from_filename(self, filename: str) -> Dict[str, Any]:
        """
        Extract movie metadata from filename.

        Args:
            filename: The filename to parse

        Returns:
            Dictionary containing extracted metadata
        """
        metadata = {
            'title': '',
            'year': None,
            'quality': None,
            'source': None,
            'codec': None,
            'audio_codec': None,
            'file_type': 'movie',  # Default to movie
            'is_series': False,
            'season': None,
            'episode': None,
        }

        # Clean filename (remove extension and path)
        clean_name = self._clean_filename(filename)

        # Extract year
        year_match = self.year_pattern.search(clean_name)
        if year_match:
            year = int(year_match.group())
            if 1900 <= year <= 2030:  # Reasonable year range
                metadata['year'] = year
                # Split title from year
                title_part = clean_name[:year_match.start()].strip()
                metadata['title'] = self._clean_title(title_part)

        # Check if it's a series
        if self.episode_pattern.search(clean_name) or self.season_pattern.search(clean_name):
            metadata['file_type'] = 'series'
            metadata['is_series'] = True

            # Extract season/episode info
            ep_match = self.episode_pattern.search(clean_name)
            if ep_match:
                ep_info = ep_match.group()
                season_match = re.search(r'S(\d+)', ep_info, re.IGNORECASE)
                episode_match = re.search(r'E(\d+)', ep_info, re.IGNORECASE)
                if season_match:
                    metadata['season'] = int(season_match.group(1))
                if episode_match:
                    metadata['episode'] = int(episode_match.group(1))

        # Extract quality
        for quality, pattern in self.QUALITY_PATTERNS.items():
            if pattern.search(clean_name):
                metadata['quality'] = quality
                break

        # Extract source
        for source, pattern in self.SOURCE_PATTERNS.items():
            if pattern.search(clean_name):
                metadata['source'] = source.replace('-', '').replace('.', '')
                break

        # Extract codec
        for codec, pattern in self.CODEC_PATTERNS.items():
            if pattern.search(clean_name):
                metadata['codec'] = codec
                break

        # Extract audio codec
        for audio, pattern in self.AUDIO_PATTERNS.items():
            if pattern.search(clean_name):
                metadata['audio_codec'] = audio
                break

        # If no title extracted yet, use the whole clean name
        if not metadata['title']:
            metadata['title'] = self._clean_title(clean_name)

        return metadata

    def extract_from_telegram_data(self, file_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract metadata from Telegram file information.

        Args:
            file_data: Telegram file data from API

        Returns:
            Dictionary with Telegram-specific metadata
        """
        metadata = {}

        # File size
        if 'file_size' in file_data:
            metadata['file_size'] = file_data['file_size']

        # MIME type
        if 'mime_type' in file_data:
            metadata['mime_type'] = file_data['mime_type']

        # Date information
        if 'date' in file_data:
            metadata['telegram_date'] = datetime.fromtimestamp(file_data['date'])

        # File ID and unique ID
        if 'file_id' in file_data:
            metadata['file_id'] = file_data['file_id']

        if 'file_unique_id' in file_data:
            metadata['file_unique_id'] = file_data['file_unique_id']

        return metadata

    def combine_metadata(self, filename_metadata: Dict[str, Any],
                        telegram_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Combine filename and Telegram metadata into complete movie record.

        Args:
            filename_metadata: Metadata extracted from filename
            telegram_metadata: Metadata from Telegram API

        Returns:
            Complete movie metadata dictionary
        """
        combined = {}

        # Combine all metadata
        combined.update(filename_metadata)
        combined.update(telegram_metadata)

        # Set defaults for missing fields
        combined.setdefault('title', 'Unknown Title')
        combined.setdefault('year', None)
        combined.setdefault('quality', 'unknown')
        combined.setdefault('source', 'unknown')
        combined.setdefault('codec', 'unknown')
        combined.setdefault('audio_codec', 'unknown')
        combined.setdefault('file_type', 'movie')
        combined.setdefault('is_series', False)

        # Generate searchable title
        combined['search_title'] = self._generate_search_title(combined['title'])

        return combined

    def _clean_filename(self, filename: str) -> str:
        """Clean filename by removing extension and path."""
        # Remove path
        filename = filename.split('/')[-1].split('\\')[-1]

        # Remove extension
        for ext in self.MOVIE_EXTENSIONS:
            if filename.lower().endswith(ext):
                filename = filename[:-len(ext)]
                break

        return filename

    def _clean_title(self, title: str) -> str:
        """Clean and format movie title."""
        # Remove common separators and extra spaces
        title = re.sub(r'[._-]', ' ', title)

        # Remove quality, source, codec info (common patterns)
        patterns_to_remove = [
            r'\b\d{3,4}p\b',  # 720p, 1080p, etc.
            r'\b(bluray|webdl|bd|br|dvd|cam|hdtv|scr)\b',  # sources
            r'\b(x264|x265|h264|h265|hevc|avc|divx|xvid)\b',  # codecs
            r'\b(aac|ac3|dts|mp3|flac|dd5\.1)\b',  # audio
            r'\b(19|20)\d{2}\b',  # years
            r'\b[Ss]\d+[Ee]\d+\b',  # episodes
        ]

        for pattern in patterns_to_remove:
            title = re.sub(pattern, '', title, flags=re.IGNORECASE)

        # Clean up extra spaces and dots
        title = re.sub(r'\s+', ' ', title)
        title = re.sub(r'\.+', '.', title)
        title = title.strip(' .')

        # Title case
        return title.title() if title else 'Unknown Title'

    def _generate_search_title(self, title: str) -> str:
        """Generate searchable version of title."""
        # Remove special characters, keep only alphanumeric and spaces
        search_title = re.sub(r'[^a-zA-Z0-9\s]', '', title)
        return search_title.lower().strip()

    def is_movie_file(self, filename: str) -> bool:
        """Check if file is a movie file based on extension."""
        filename = filename.lower()
        return any(filename.endswith(ext) for ext in self.MOVIE_EXTENSIONS)


# Global instance
metadata_extractor = MovieMetadataExtractor()


def extract_movie_metadata(filename: str, telegram_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Convenience function to extract complete movie metadata.

    Args:
        filename: Movie filename
        telegram_data: Optional Telegram file data

    Returns:
        Complete movie metadata
    """
    filename_meta = metadata_extractor.extract_from_filename(filename)

    telegram_meta = {}
    if telegram_data:
        telegram_meta = metadata_extractor.extract_from_telegram_data(telegram_data)

    return metadata_extractor.combine_metadata(filename_meta, telegram_meta)
