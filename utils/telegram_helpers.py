"""
Telegram helper utilities for channel management and verification.
"""

import logging
import re
from typing import Op<PERSON>, Dict, Any, Union
from telegram import <PERSON><PERSON>, <PERSON><PERSON>, Chat<PERSON><PERSON>ber
from telegram.error import TelegramError, Forbidden, BadRequest

logger = logging.getLogger(__name__)


def extract_channel_identifier(text: str) -> Optional[Dict[str, Any]]:
    """
    Extract channel ID or username from various input formats.
    
    Args:
        text: Channel identifier (@username, username, or -1001234567890)
        
    Returns:
        Dict with channel_id (int) and/or username (str), or None if invalid
    """
    if not text:
        return None
    
    text = text.strip()
    
    # Handle channel ID format (-1001234567890)
    if text.startswith('-100'):
        try:
            channel_id = int(text)
            return {'channel_id': channel_id, 'type': 'id'}
        except ValueError:
            return None
    
    # Handle username format (@username or username)
    if text.startswith('@'):
        username = text[1:]  # Remove @
    else:
        username = text
    
    # Validate username format
    if re.match(r'^[a-zA-Z][a-zA-Z0-9_]{4,31}$', username):
        return {'username': username, 'type': 'username'}
    
    return None


async def get_channel_info_from_telegram(bot: Bot, channel_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Get channel information from Telegram API.
    
    Args:
        bot: Telegram bot instance
        channel_info: Dict with channel_id or username
        
    Returns:
        Dict with channel information or None if not found
    """
    try:
        if 'channel_id' in channel_info:
            chat = await bot.get_chat(channel_info['channel_id'])
        elif 'username' in channel_info:
            chat = await bot.get_chat(f"@{channel_info['username']}")
        else:
            return None
        
        # Extract relevant information
        result = {
            'channel_id': chat.id,
            'title': chat.title,
            'type': chat.type,
            'username': chat.username,
            'description': chat.description,
            'member_count': None,  # Will be populated if available
            'invite_link': None
        }
        
        # Try to get member count (may not be available for all channels)
        try:
            member_count = await bot.get_chat_member_count(chat.id)
            result['member_count'] = member_count
        except (TelegramError, AttributeError):
            pass
        
        # Try to get invite link if bot is admin
        try:
            invite_link = await bot.export_chat_invite_link(chat.id)
            result['invite_link'] = invite_link
        except (TelegramError, Forbidden):
            pass
        
        return result
        
    except (TelegramError, BadRequest, Forbidden) as e:
        logger.error(f"Error getting channel info: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error getting channel info: {e}")
        return None


async def verify_bot_admin_permissions(bot: Bot, channel_id: int) -> bool:
    """
    Verify that the bot has admin permissions in the specified channel.
    
    Args:
        bot: Telegram bot instance
        channel_id: Channel ID to check
        
    Returns:
        True if bot has admin permissions, False otherwise
    """
    try:
        # Get bot's member status in the channel
        bot_member = await bot.get_chat_member(channel_id, bot.id)
        
        # Check if bot is admin or creator
        if bot_member.status in ['administrator', 'creator']:
            return True
        
        return False
        
    except (TelegramError, Forbidden, BadRequest) as e:
        logger.error(f"Error checking bot permissions in channel {channel_id}: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error checking bot permissions: {e}")
        return False


async def get_bot_permissions_in_channel(bot: Bot, channel_id: int) -> Optional[Dict[str, bool]]:
    """
    Get detailed bot permissions in a channel.
    
    Args:
        bot: Telegram bot instance
        channel_id: Channel ID to check
        
    Returns:
        Dict with permission details or None if not accessible
    """
    try:
        bot_member = await bot.get_chat_member(channel_id, bot.id)
        
        if bot_member.status == 'creator':
            # Creator has all permissions
            return {
                'is_admin': True,
                'can_delete_messages': True,
                'can_manage_chat': True,
                'can_post_messages': True,
                'can_edit_messages': True,
                'can_invite_users': True,
                'can_restrict_members': True,
                'can_pin_messages': True,
                'can_promote_members': True,
                'status': 'creator'
            }
        elif bot_member.status == 'administrator':
            # Extract specific admin permissions
            return {
                'is_admin': True,
                'can_delete_messages': bot_member.can_delete_messages,
                'can_manage_chat': bot_member.can_manage_chat,
                'can_post_messages': getattr(bot_member, 'can_post_messages', False),
                'can_edit_messages': getattr(bot_member, 'can_edit_messages', False),
                'can_invite_users': bot_member.can_invite_users,
                'can_restrict_members': bot_member.can_restrict_members,
                'can_pin_messages': bot_member.can_pin_messages,
                'can_promote_members': bot_member.can_promote_members,
                'status': 'administrator'
            }
        else:
            return {
                'is_admin': False,
                'status': bot_member.status
            }
            
    except (TelegramError, Forbidden, BadRequest) as e:
        logger.error(f"Error getting bot permissions in channel {channel_id}: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error getting bot permissions: {e}")
        return None


async def check_channel_accessibility(bot: Bot, channel_identifier: Union[str, int]) -> Dict[str, Any]:
    """
    Comprehensive check of channel accessibility and bot permissions.
    
    Args:
        bot: Telegram bot instance
        channel_identifier: Channel ID or username
        
    Returns:
        Dict with accessibility status and details
    """
    result = {
        'accessible': False,
        'exists': False,
        'bot_is_member': False,
        'bot_is_admin': False,
        'channel_info': None,
        'permissions': None,
        'error': None
    }
    
    try:
        # Get channel information
        chat = await bot.get_chat(channel_identifier)
        result['exists'] = True
        result['accessible'] = True
        result['channel_info'] = {
            'id': chat.id,
            'title': chat.title,
            'type': chat.type,
            'username': chat.username
        }
        
        # Check bot membership and permissions
        try:
            bot_member = await bot.get_chat_member(chat.id, bot.id)
            result['bot_is_member'] = True
            
            if bot_member.status in ['administrator', 'creator']:
                result['bot_is_admin'] = True
                result['permissions'] = await get_bot_permissions_in_channel(bot, chat.id)
            
        except (Forbidden, BadRequest):
            # Bot is not a member or has no access
            result['bot_is_member'] = False
            result['error'] = "Bot is not a member of this channel"
        
    except Forbidden:
        result['error'] = "Channel is private and bot has no access"
    except BadRequest as e:
        if "chat not found" in str(e).lower():
            result['error'] = "Channel not found"
        else:
            result['error'] = f"Bad request: {e}"
    except TelegramError as e:
        result['error'] = f"Telegram error: {e}"
    except Exception as e:
        result['error'] = f"Unexpected error: {e}"
        logger.error(f"Unexpected error checking channel accessibility: {e}")
    
    return result


async def format_channel_status_message(channel_data: Dict[str, Any], permissions: Optional[Dict[str, bool]] = None) -> str:
    """
    Format a comprehensive channel status message.
    
    Args:
        channel_data: Channel information from database
        permissions: Bot permissions in the channel
        
    Returns:
        Formatted status message
    """
    channel_name = channel_data.get('channel_name', 'Unknown Channel')
    channel_id = channel_data['channel_id']
    is_monitored = channel_data.get('is_monitored', False)
    is_searchable = channel_data.get('is_searchable', True)
    
    # Status indicators
    monitor_emoji = "🟢" if is_monitored else "🔴"
    search_emoji = "🟢" if is_searchable else "🔴"
    
    message = f"📺 <b>{channel_name}</b>\n\n"
    message += f"🆔 <b>ID:</b> <code>{channel_id}</code>\n"
    
    if channel_data.get('channel_username'):
        message += f"👤 <b>Username:</b> @{channel_data['channel_username']}\n"
    
    message += f"\n<b>📊 Status:</b>\n"
    message += f"• Monitoring: {monitor_emoji} {'Enabled' if is_monitored else 'Disabled'}\n"
    message += f"• Search: {search_emoji} {'Enabled' if is_searchable else 'Disabled'}\n"
    
    if permissions:
        message += f"\n<b>🔐 Bot Permissions:</b>\n"
        if permissions.get('is_admin'):
            message += f"• Admin Status: ✅ {permissions.get('status', 'administrator').title()}\n"
            if permissions.get('can_delete_messages'):
                message += f"• Delete Messages: ✅\n"
            if permissions.get('can_post_messages'):
                message += f"• Post Messages: ✅\n"
        else:
            message += f"• Admin Status: ❌ Not Admin\n"
    
    # Add timestamps
    if channel_data.get('created_at'):
        created_date = channel_data['created_at'].strftime('%Y-%m-%d %H:%M')
        message += f"\n📅 <b>Added:</b> {created_date} UTC"
    
    return message


def validate_channel_identifier(identifier: str) -> Dict[str, Any]:
    """
    Validate and normalize channel identifier.
    
    Args:
        identifier: Raw channel identifier input
        
    Returns:
        Dict with validation result and normalized identifier
    """
    result = {
        'valid': False,
        'type': None,
        'normalized': None,
        'error': None
    }
    
    if not identifier or not isinstance(identifier, str):
        result['error'] = "Empty or invalid identifier"
        return result
    
    identifier = identifier.strip()
    
    # Check for channel ID format
    if identifier.startswith('-100'):
        try:
            channel_id = int(identifier)
            # Telegram channel IDs are negative numbers starting with -100
            # Valid range is approximately -1001000000000 to -1002147483647
            if channel_id < -1000000000000:  # Valid Telegram channel ID range
                result['valid'] = True
                result['type'] = 'id'
                result['normalized'] = channel_id
            else:
                result['error'] = "Invalid channel ID format"
        except ValueError:
            result['error'] = "Invalid channel ID format"
    
    # Check for username format
    elif identifier.startswith('@') or re.match(r'^[a-zA-Z][a-zA-Z0-9_]{4,31}$', identifier):
        username = identifier[1:] if identifier.startswith('@') else identifier
        if re.match(r'^[a-zA-Z][a-zA-Z0-9_]{4,31}$', username):
            result['valid'] = True
            result['type'] = 'username'
            result['normalized'] = f"@{username}"
        else:
            result['error'] = "Invalid username format"
    else:
        result['error'] = "Invalid channel identifier format"
    
    return result
